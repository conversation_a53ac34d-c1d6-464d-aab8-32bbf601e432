-- ScholarAI Initial Database Schema Migration
-- FERPA-Compliant Multi-Tenant Educational Platform
-- This migration creates the core schema with Row Level Security

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom enums
CREATE TYPE user_role AS ENUM ('super_admin', 'org_admin', 'teacher', 'parent_guardian', 'student');
CREATE TYPE account_type AS ENUM ('individual', 'school', 'district', 'organization');
CREATE TYPE age_group AS ENUM ('pre_k', 'elementary', 'middle', 'high', 'adult');

-- Users table with FERPA compliance
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified TIMESTAMP,
    hashed_password VARCHAR(255),
    
    -- Profile information
    first_name VARCHA<PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    display_name <PERSON><PERSON><PERSON><PERSON>(200),
    avatar_url TEXT,
    
    -- Role and permissions
    role user_role NOT NULL DEFAULT 'student',
    account_type account_type NOT NULL DEFAULT 'individual',
    
    -- FERPA compliance fields
    is_minor BOOLEAN DEFAULT FALSE,
    parental_consent_given BOOLEAN DEFAULT FALSE,
    parental_consent_date TIMESTAMP,
    consent_document TEXT,
    
    -- Age-appropriate content filtering
    age_group age_group DEFAULT 'elementary',
    content_filter_level VARCHAR(20) DEFAULT 'strict',
    
    -- Account status
    is_active BOOLEAN DEFAULT TRUE,
    is_email_verified BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES users(id),
    
    -- FERPA data retention
    data_retention_date TIMESTAMP,
    ferpa_directory_info BOOLEAN DEFAULT FALSE
);

-- Organizations table for multi-tenancy
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Basic organization info
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    description TEXT,
    logo_url TEXT,
    website_url TEXT,
    
    -- Organization type and configuration
    type account_type NOT NULL DEFAULT 'school',
    subdomain VARCHAR(100) UNIQUE,
    
    -- Contact information
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    
    -- Address information
    address_line_1 VARCHAR(255),
    address_line_2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'US',
    
    -- FERPA compliance settings
    ferpa_officer_name VARCHAR(255),
    ferpa_officer_email VARCHAR(255),
    ferpa_officer_phone VARCHAR(50),
    ferpa_annual_notification BOOLEAN DEFAULT TRUE,
    
    -- Subscription and billing
    subscription_tier VARCHAR(50) DEFAULT 'basic',
    max_students INTEGER DEFAULT 100,
    max_storage_gb DECIMAL(10,2) DEFAULT 10.00,
    
    -- Security settings
    require_two_factor BOOLEAN DEFAULT FALSE,
    password_policy TEXT,
    session_timeout_minutes INTEGER DEFAULT 60,
    
    -- Status and configuration
    is_active BOOLEAN DEFAULT TRUE,
    settings JSONB,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES users(id)
);

-- Parent-Child relationships for family accounts
CREATE TABLE parent_child_relations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    parent_id UUID REFERENCES users(id) NOT NULL,
    child_id UUID REFERENCES users(id) NOT NULL,
    
    -- Relationship details
    relationship_type VARCHAR(50) DEFAULT 'parent',
    has_educational_rights BOOLEAN DEFAULT TRUE,
    can_view_progress BOOLEAN DEFAULT TRUE,
    can_manage_account BOOLEAN DEFAULT TRUE,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES users(id),
    
    UNIQUE(parent_id, child_id)
);

-- Organization memberships
CREATE TABLE organization_memberships (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID REFERENCES organizations(id) NOT NULL,
    user_id UUID REFERENCES users(id) NOT NULL,
    
    -- Membership details
    role VARCHAR(50) NOT NULL,
    title VARCHAR(100),
    department VARCHAR(100),
    
    -- Status and dates
    status VARCHAR(20) DEFAULT 'active',
    joined_at TIMESTAMP DEFAULT NOW() NOT NULL,
    left_at TIMESTAMP,
    
    -- Permissions within the organization
    permissions JSONB,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
    created_by UUID REFERENCES users(id),
    
    UNIQUE(organization_id, user_id)
);

-- Materials table with content safety
CREATE TABLE materials (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) NOT NULL,
    organization_id UUID REFERENCES organizations(id),
    
    -- Material metadata
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    description TEXT,
    
    -- Content information (encrypted for sensitive content)
    content TEXT,
    file_url TEXT,
    file_size INTEGER,
    file_mime_type VARCHAR(100),
    
    -- Content safety and approval
    safety_status VARCHAR(20) DEFAULT 'pending',
    safety_score DECIMAL(5,2),
    safety_review TEXT,
    
    -- Educational categorization
    subject VARCHAR(100),
    topics TEXT,
    difficulty VARCHAR(20),
    age_group age_group,
    
    -- Usage and sharing
    is_public BOOLEAN DEFAULT FALSE,
    share_with_classroom BOOLEAN DEFAULT FALSE,
    share_with_organization BOOLEAN DEFAULT FALSE,
    
    -- Status and metadata
    status VARCHAR(20) DEFAULT 'active',
    last_accessed_at TIMESTAMP,
    access_count INTEGER DEFAULT 0,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
    deleted_at TIMESTAMP
);

-- FERPA-compliant audit logging
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Context identification
    user_id UUID REFERENCES users(id),
    organization_id UUID REFERENCES organizations(id),
    session_id UUID,
    
    -- Action details
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID,
    
    -- FERPA compliance fields
    student_data_accessed BOOLEAN DEFAULT FALSE,
    data_classification VARCHAR(50),
    legal_basis VARCHAR(100),
    
    -- Action context
    description TEXT,
    reason TEXT,
    
    -- Change tracking
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT,
    
    -- Request metadata
    ip_address VARCHAR(45),
    user_agent TEXT,
    method VARCHAR(10),
    endpoint VARCHAR(500),
    request_id UUID,
    
    -- Result information
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    http_status VARCHAR(10),
    
    -- Privacy and compliance
    data_retention_date TIMESTAMP,
    sensitive_data_masked BOOLEAN DEFAULT TRUE,
    
    -- Risk and security
    risk_level VARCHAR(20) DEFAULT 'low',
    security_flags TEXT,
    
    -- Audit fields
    created_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Row Level Security Policies

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE parent_child_relations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Users can only see their own data and children's data
CREATE POLICY "Users can view own profile and children" ON users
    FOR SELECT USING (
        auth.uid()::uuid = id OR
        auth.uid()::uuid IN (
            SELECT parent_id FROM parent_child_relations 
            WHERE child_id = users.id
        ) OR
        auth.uid()::uuid IN (
            SELECT user_id FROM organization_memberships 
            WHERE role IN ('org_admin', 'teacher') 
            AND organization_id IN (
                SELECT organization_id FROM organization_memberships 
                WHERE user_id = users.id
            )
        )
    );

-- Users can only update their own data (with restrictions for minors)
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (
        auth.uid()::uuid = id AND
        (NOT is_minor OR parental_consent_given)
    );

-- Organization access control
CREATE POLICY "Organization members can view organization" ON organizations
    FOR SELECT USING (
        auth.uid()::uuid IN (
            SELECT user_id FROM organization_memberships 
            WHERE organization_id = organizations.id
        )
    );

-- Parent-child relationship access
CREATE POLICY "Parents can manage their children" ON parent_child_relations
    FOR ALL USING (
        auth.uid()::uuid = parent_id OR
        auth.uid()::uuid = child_id
    );

-- Material access control with FERPA compliance
CREATE POLICY "Users can access their materials and shared materials" ON materials
    FOR SELECT USING (
        auth.uid()::uuid = user_id OR
        (is_public = TRUE) OR
        (share_with_organization = TRUE AND auth.uid()::uuid IN (
            SELECT user_id FROM organization_memberships 
            WHERE organization_id = materials.organization_id
        )) OR
        -- Parents can access children's materials
        auth.uid()::uuid IN (
            SELECT parent_id FROM parent_child_relations 
            WHERE child_id = materials.user_id AND has_educational_rights = TRUE
        )
    );

-- Audit log access (restricted to admins and data subjects)
CREATE POLICY "Audit log access control" ON audit_logs
    FOR SELECT USING (
        auth.uid()::uuid = user_id OR
        auth.uid()::uuid IN (
            SELECT user_id FROM organization_memberships 
            WHERE role = 'org_admin' 
            AND organization_id = audit_logs.organization_id
        )
    );

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_organization ON users(id) WHERE account_type != 'individual';
CREATE INDEX idx_organizations_subdomain ON organizations(subdomain);
CREATE INDEX idx_organization_memberships_user ON organization_memberships(user_id);
CREATE INDEX idx_organization_memberships_org ON organization_memberships(organization_id);
CREATE INDEX idx_parent_child_parent ON parent_child_relations(parent_id);
CREATE INDEX idx_parent_child_child ON parent_child_relations(child_id);
CREATE INDEX idx_materials_user ON materials(user_id);
CREATE INDEX idx_materials_organization ON materials(organization_id);
CREATE INDEX idx_materials_safety ON materials(safety_status);
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_organization ON audit_logs(organization_id);
CREATE INDEX idx_audit_logs_action_date ON audit_logs(action, created_at);
CREATE INDEX idx_audit_logs_student_data ON audit_logs(student_data_accessed, created_at);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_parent_child_relations_updated_at BEFORE UPDATE ON parent_child_relations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organization_memberships_updated_at BEFORE UPDATE ON organization_memberships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_materials_updated_at BEFORE UPDATE ON materials
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();