import {z} from 'zod';

// Content safety types
export interface SafetyResult {
	isAppropriate: boolean;
	violations: string[];
	confidence: number;
	riskLevel: 'low' | 'medium' | 'high';
	suggestions?: string[];
}

export interface FilterConfig {
	strictMode: boolean;
	ageGroup: 'elementary' | 'middle' | 'high' | 'adult';
	educationalContext: boolean;
}

// Age-appropriate content patterns
const SAFETY_PATTERNS = {
	// Inappropriate content patterns
	violence:
		/\b(kill|murder|weapon|gun|knife|violence|attack|hurt|harm|fight|war|blood|death)\b/gi,
	adult: /\b(sex|porn|adult|explicit|mature|inappropriate|nude|naked)\b/gi,
	hate: /\b(hate|racist|discrimination|bullying|harassment|offensive|slur)\b/gi,
	drugs:
		/\b(drug|alcohol|marijuana|cocaine|smoking|drinking|beer|wine|drunk)\b/gi,
	profanity: /\b(damn|hell|crap|stupid|idiot|dumb|shut up)\b/gi,

	// Prompt injection patterns
	injection: [
		/ignore.+previous.+instructions/gi,
		/system.+prompt/gi,
		/act.+as.+(?:jailbreak|dan|evil)/gi,
		/forget.+everything/gi,
		/new.+instructions/gi,
		/override.+safety/gi,
	],

	// Educational positive patterns
	educational:
		/\b(learn|study|education|knowledge|academic|school|teach|homework|assignment|research|facts|concept|theory|principle|example|definition)\b/gi,
	subjects:
		/\b(math|science|history|literature|geography|biology|chemistry|physics|english|art|music|language)\b/gi,
};

// Age-group specific filtering rules
const AGE_GROUP_RULES = {
	elementary: {
		maxComplexity: 5, // Reading level
		allowedTopics: [
			'basic-science',
			'math',
			'reading',
			'art',
			'animals',
			'nature',
		],
		blockedTopics: [
			'politics',
			'religion',
			'controversial',
			'complex-social-issues',
		],
		strictProfanity: true,
	},
	middle: {
		maxComplexity: 8,
		allowedTopics: ['science', 'history', 'literature', 'geography', 'culture'],
		blockedTopics: ['adult-themes', 'graphic-content'],
		strictProfanity: true,
	},
	high: {
		maxComplexity: 12,
		allowedTopics: [
			'advanced-science',
			'philosophy',
			'economics',
			'social-studies',
		],
		blockedTopics: ['explicit-content'],
		strictProfanity: false,
	},
	adult: {
		maxComplexity: 15,
		allowedTopics: ['all-academic'],
		blockedTopics: ['illegal-content'],
		strictProfanity: false,
	},
};

export class ContentSafetyFilter {
	private config: FilterConfig;

	constructor(
		config: FilterConfig = {
			strictMode: true,
			ageGroup: 'middle',
			educationalContext: true,
		}
	) {
		this.config = config;
	}

	/**
	 * Main content safety check function
	 */
	async checkContent(text: string): Promise<SafetyResult> {
		const violations: string[] = [];
		let riskLevel: 'low' | 'medium' | 'high' = 'low';
		let confidence = 0.8;

		try {
			// 1. Check for inappropriate content
			const inappropriateCheck = this.checkInappropriateContent(text);
			if (inappropriateCheck.violations.length > 0) {
				violations.push(...inappropriateCheck.violations);
				riskLevel = 'high';
			}

			// 2. Check for prompt injection attempts
			const injectionCheck = this.checkPromptInjection(text);
			if (injectionCheck.violations.length > 0) {
				violations.push(...injectionCheck.violations);
				riskLevel = 'high';
				confidence = 0.9;
			}

			// 3. Age-appropriateness check
			const ageCheck = this.checkAgeAppropriate(text);
			if (!ageCheck.isAppropriate) {
				violations.push(...ageCheck.violations);
				riskLevel = riskLevel === 'high' ? 'high' : 'medium';
			}

			// 4. Educational value check
			const educationalCheck = this.checkEducationalValue(text);
			if (!educationalCheck.isEducational && this.config.educationalContext) {
				violations.push('non-educational-content');
				riskLevel = riskLevel === 'high' ? 'high' : 'medium';
			}

			// 5. Reading complexity check
			const complexityCheck = this.checkReadingComplexity(text);
			if (!complexityCheck.isAppropriate) {
				violations.push(`content-too-complex-for-${this.config.ageGroup}`);
				if (riskLevel === 'low') riskLevel = 'medium';
			}

			return {
				isAppropriate: violations.length === 0,
				violations,
				confidence,
				riskLevel,
				suggestions: this.generateSuggestions(violations),
			};
		} catch (error) {
			console.error('Content safety check error:', error);
			return {
				isAppropriate: false,
				violations: ['safety-check-failed'],
				confidence: 0.1,
				riskLevel: 'high',
				suggestions: ['Please try uploading different content'],
			};
		}
	}

	/**
	 * Check for inappropriate content patterns
	 */
	private checkInappropriateContent(text: string): {
		violations: string[];
		confidence: number;
	} {
		const violations: string[] = [];
		const lowerText = text.toLowerCase();

		// Check each category
		for (const [category, pattern] of Object.entries(SAFETY_PATTERNS)) {
			if (
				category === 'injection' ||
				category === 'educational' ||
				category === 'subjects'
			)
				continue;

			if (pattern instanceof RegExp && pattern.test(lowerText)) {
				violations.push(`inappropriate-${category}`);
			}
		}

		// Special handling for profanity based on age group
		if (
			this.config.strictMode ||
			AGE_GROUP_RULES[this.config.ageGroup].strictProfanity
		) {
			if (SAFETY_PATTERNS.profanity.test(lowerText)) {
				violations.push('inappropriate-language');
			}
		}

		return {violations, confidence: 0.85};
	}

	/**
	 * Check for prompt injection attempts
	 */
	private checkPromptInjection(text: string): {
		violations: string[];
		confidence: number;
	} {
		const violations: string[] = [];

		for (const pattern of SAFETY_PATTERNS.injection) {
			if (pattern.test(text)) {
				violations.push('prompt-injection-detected');
				break; // One detection is enough
			}
		}

		return {violations, confidence: 0.9};
	}

	/**
	 * Check age-appropriateness based on content and complexity
	 */
	private checkAgeAppropriate(text: string): {
		isAppropriate: boolean;
		violations: string[];
	} {
		const violations: string[] = [];
		const rules = AGE_GROUP_RULES[this.config.ageGroup];

		// Check blocked topics (this is a simplified check - in production, use ML models)
		const suspiciousPatterns = [
			/politics|political|government|election/gi,
			/religion|religious|god|church|worship/gi,
			/controversial|debate|argument|conflict/gi,
		];

		if (this.config.ageGroup === 'elementary') {
			for (const pattern of suspiciousPatterns) {
				if (pattern.test(text)) {
					violations.push('topic-not-age-appropriate');
					break;
				}
			}
		}

		return {
			isAppropriate: violations.length === 0,
			violations,
		};
	}

	/**
	 * Check if content has educational value
	 */
	private checkEducationalValue(text: string): {
		isEducational: boolean;
		score: number;
	} {
		const educationalMatches = (text.match(SAFETY_PATTERNS.educational) || [])
			.length;
		const subjectMatches = (text.match(SAFETY_PATTERNS.subjects) || []).length;

		const score =
			((educationalMatches + subjectMatches * 2) / text.split(' ').length) *
			100;

		return {
			isEducational: score > 2 || educationalMatches >= 2,
			score,
		};
	}

	/**
	 * Check reading complexity using simple heuristics
	 */
	private checkReadingComplexity(text: string): {
		isAppropriate: boolean;
		level: number;
	} {
		const sentences = text
			.split(/[.!?]+/)
			.filter((s) => s.trim().length > 0).length;
		const words = text.split(/\s+/).length;
		const avgWordsPerSentence = words / Math.max(sentences, 1);

		// Simple complexity estimation (Flesch-Kincaid approximation)
		const complexityLevel = Math.min(avgWordsPerSentence / 2, 15);
		const maxAllowed = AGE_GROUP_RULES[this.config.ageGroup].maxComplexity;

		return {
			isAppropriate: complexityLevel <= maxAllowed,
			level: complexityLevel,
		};
	}

	/**
	 * Generate helpful suggestions for content improvement
	 */
	private generateSuggestions(violations: string[]): string[] {
		const suggestions: string[] = [];

		if (violations.includes('inappropriate-violence')) {
			suggestions.push(
				'Consider removing violent content and focusing on peaceful examples'
			);
		}

		if (violations.includes('inappropriate-language')) {
			suggestions.push(
				'Please use appropriate language suitable for educational settings'
			);
		}

		if (violations.includes('non-educational-content')) {
			suggestions.push(
				'Try adding more educational context or learning objectives'
			);
		}

		if (violations.includes('prompt-injection-detected')) {
			suggestions.push(
				'Please provide straightforward educational content without special instructions'
			);
		}

		if (violations.some((v) => v.includes('too-complex'))) {
			suggestions.push(
				`Simplify the language for ${this.config.ageGroup} level students`
			);
		}

		return suggestions;
	}

	/**
	 * Update filter configuration
	 */
	updateConfig(newConfig: Partial<FilterConfig>): void {
		this.config = {...this.config, ...newConfig};
	}

	/**
	 * Get current filter statistics
	 */
	getFilterStats(): FilterConfig & {version: string} {
		return {
			...this.config,
			version: '1.0.0',
		};
	}
}

// Export singleton instance with default config
export const contentSafetyFilter = new ContentSafetyFilter();

// Custom error class for safety violations
export class ContentSafetyError extends Error {
	constructor(
		message: string,
		public violations: string[],
		public riskLevel: 'low' | 'medium' | 'high'
	) {
		super(message);
		this.name = 'ContentSafetyError';
	}
}

// Utility function for quick safety checks
export async function isContentSafe(
	text: string,
	config?: Partial<FilterConfig>
): Promise<boolean> {
	const filter = new ContentSafetyFilter(config as FilterConfig);
	const result = await filter.checkContent(text);
	return result.isAppropriate;
}

// Export types are already defined as interfaces above
