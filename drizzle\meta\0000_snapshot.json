{"id": "a8c45f21-608b-4263-af33-35c06ae7956d", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.activity_logs": {"name": "activity_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "activity_type": {"name": "activity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "activity_name": {"name": "activity_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "duration_seconds": {"name": "duration_seconds", "type": "integer", "primaryKey": false, "notNull": false}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"activity_logs_user_activity_idx": {"name": "activity_logs_user_activity_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "activity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_logs_org_activity_idx": {"name": "activity_logs_org_activity_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "activity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_logs_type_time_idx": {"name": "activity_logs_type_time_idx", "columns": [{"expression": "activity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"activity_logs_user_id_users_id_fk": {"name": "activity_logs_user_id_users_id_fk", "tableFrom": "activity_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "activity_logs_organization_id_organizations_id_fk": {"name": "activity_logs_organization_id_organizations_id_fk", "tableFrom": "activity_logs", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.content_analytics": {"name": "content_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "material_id": {"name": "material_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "material_name": {"name": "material_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "material_type": {"name": "material_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "age_group": {"name": "age_group", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "analytics_date": {"name": "analytics_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "period_type": {"name": "period_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "quiz_count": {"name": "quiz_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "average_quiz_score": {"name": "average_quiz_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "flip_card_generations": {"name": "flip_card_generations", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "difficulty_score": {"name": "difficulty_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "engagement_score": {"name": "engagement_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "educational_value": {"name": "educational_value", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "positive_reactions": {"name": "positive_reactions", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "negative_reactions": {"name": "negative_reactions", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "safety_score": {"name": "safety_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "appropriateness_score": {"name": "appropriateness_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"content_analytics_material_date_idx": {"name": "content_analytics_material_date_idx", "columns": [{"expression": "material_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "analytics_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_analytics_subject_date_idx": {"name": "content_analytics_subject_date_idx", "columns": [{"expression": "subject", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "analytics_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"content_analytics_organization_id_organizations_id_fk": {"name": "content_analytics_organization_id_organizations_id_fk", "tableFrom": "content_analytics", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.learning_analytics": {"name": "learning_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "classroom_id": {"name": "classroom_id", "type": "uuid", "primaryKey": false, "notNull": false}, "analytics_date": {"name": "analytics_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "period_type": {"name": "period_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "sessions_count": {"name": "sessions_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "total_time_spent_minutes": {"name": "total_time_spent_minutes", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "average_session_time_minutes": {"name": "average_session_time_minutes", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "quizzes_completed": {"name": "quizzes_completed", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "average_quiz_score": {"name": "average_quiz_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "topics_studied": {"name": "topics_studied", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "materials_created": {"name": "materials_created", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "performance_improvement": {"name": "performance_improvement", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "consistency_score": {"name": "consistency_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "engagement_score": {"name": "engagement_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "subject_performance": {"name": "subject_performance", "type": "jsonb", "primaryKey": false, "notNull": false}, "topics_strengths": {"name": "topics_strengths", "type": "jsonb", "primaryKey": false, "notNull": false}, "topics_weaknesses": {"name": "topics_weaknesses", "type": "jsonb", "primaryKey": false, "notNull": false}, "preferred_study_time": {"name": "preferred_study_time", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "average_questions_per_quiz": {"name": "average_questions_per_quiz", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "retry_pattern": {"name": "retry_pattern", "type": "jsonb", "primaryKey": false, "notNull": false}, "badges_earned": {"name": "badges_earned", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "milestones_reached": {"name": "milestones_reached", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "streak_days": {"name": "streak_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"learning_analytics_user_date_idx": {"name": "learning_analytics_user_date_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "analytics_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_analytics_org_date_idx": {"name": "learning_analytics_org_date_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "analytics_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"learning_analytics_user_id_users_id_fk": {"name": "learning_analytics_user_id_users_id_fk", "tableFrom": "learning_analytics", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "learning_analytics_organization_id_organizations_id_fk": {"name": "learning_analytics_organization_id_organizations_id_fk", "tableFrom": "learning_analytics", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "learning_analytics_classroom_id_classrooms_id_fk": {"name": "learning_analytics_classroom_id_classrooms_id_fk", "tableFrom": "learning_analytics", "tableTo": "classrooms", "columnsFrom": ["classroom_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.performance_metrics": {"name": "performance_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "metric_name": {"name": "metric_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "metric_type": {"name": "metric_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "endpoint": {"name": "endpoint", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "numeric(12, 4)", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true}, "time_window": {"name": "time_window", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"performance_metrics_metric_time_idx": {"name": "performance_metrics_metric_time_idx", "columns": [{"expression": "metric_name", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "performance_metrics_endpoint_time_idx": {"name": "performance_metrics_endpoint_time_idx", "columns": [{"expression": "endpoint", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"performance_metrics_organization_id_organizations_id_fk": {"name": "performance_metrics_organization_id_organizations_id_fk", "tableFrom": "performance_metrics", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.usage_analytics": {"name": "usage_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "analytics_date": {"name": "analytics_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "period_type": {"name": "period_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "active_users": {"name": "active_users", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "new_users": {"name": "new_users", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "total_sessions": {"name": "total_sessions", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "average_session_duration_minutes": {"name": "average_session_duration_minutes", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "quizzes_generated": {"name": "quizzes_generated", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "flip_cards_generated": {"name": "flip_cards_generated", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "materials_uploaded": {"name": "materials_uploaded", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "chat_messages_exchanged": {"name": "chat_messages_exchanged", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "average_quiz_score": {"name": "average_quiz_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "completion_rate": {"name": "completion_rate", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "feature_usage": {"name": "feature_usage", "type": "jsonb", "primaryKey": false, "notNull": false}, "popular_topics": {"name": "popular_topics", "type": "jsonb", "primaryKey": false, "notNull": false}, "popular_subjects": {"name": "popular_subjects", "type": "jsonb", "primaryKey": false, "notNull": false}, "content_flagged": {"name": "content_flagged", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "safety_violations": {"name": "safety_violations", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "average_safety_score": {"name": "average_safety_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "api_calls": {"name": "api_calls", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "error_rate": {"name": "error_rate", "type": "numeric(5, 4)", "primaryKey": false, "notNull": false}, "average_response_time_ms": {"name": "average_response_time_ms", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"usage_analytics_date_org_idx": {"name": "usage_analytics_date_org_idx", "columns": [{"expression": "analytics_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"usage_analytics_organization_id_organizations_id_fk": {"name": "usage_analytics_organization_id_organizations_id_fk", "tableFrom": "usage_analytics", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": false}, "student_data_accessed": {"name": "student_data_accessed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "data_classification": {"name": "data_classification", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "legal_basis": {"name": "legal_basis", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "reason": {"name": "reason", "type": "text", "primaryKey": false, "notNull": false}, "old_values": {"name": "old_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "new_values": {"name": "new_values", "type": "jsonb", "primaryKey": false, "notNull": false}, "changed_fields": {"name": "changed_fields", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "method": {"name": "method", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "endpoint": {"name": "endpoint", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "request_id": {"name": "request_id", "type": "uuid", "primaryKey": false, "notNull": false}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "http_status": {"name": "http_status", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "data_retention_date": {"name": "data_retention_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "sensitive_data_masked": {"name": "sensitive_data_masked", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "risk_level": {"name": "risk_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'low'"}, "security_flags": {"name": "security_flags", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"audit_logs_user_action_idx": {"name": "audit_logs_user_action_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "action", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_entity_idx": {"name": "audit_logs_entity_idx", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_student_data_idx": {"name": "audit_logs_student_data_idx", "columns": [{"expression": "student_data_accessed", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_org_date_idx": {"name": "audit_logs_org_date_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_risk_level_idx": {"name": "audit_logs_risk_level_idx", "columns": [{"expression": "risk_level", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"audit_logs_user_id_users_id_fk": {"name": "audit_logs_user_id_users_id_fk", "tableFrom": "audit_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "audit_logs_organization_id_organizations_id_fk": {"name": "audit_logs_organization_id_organizations_id_fk", "tableFrom": "audit_logs", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.consent_records": {"name": "consent_records", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "consent_type": {"name": "consent_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "purpose": {"name": "purpose", "type": "text", "primaryKey": false, "notNull": true}, "data_types": {"name": "data_types", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'active'"}, "consent_given": {"name": "consent_given", "type": "boolean", "primaryKey": false, "notNull": true}, "consent_date": {"name": "consent_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "withdrawn_at": {"name": "withdrawn_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "withdrawn_by": {"name": "withdrawn_by", "type": "uuid", "primaryKey": false, "notNull": false}, "withdrawal_reason": {"name": "withdrawal_reason", "type": "text", "primaryKey": false, "notNull": false}, "consent_method": {"name": "consent_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "document_url": {"name": "document_url", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "reminder_sent_at": {"name": "reminder_sent_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "renewal_required": {"name": "renewal_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "lawful_basis": {"name": "lawful_basis", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "minor_consent_handling": {"name": "minor_consent_handling", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"consent_records_user_consent_idx": {"name": "consent_records_user_consent_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "consent_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consent_records_student_consent_idx": {"name": "consent_records_student_consent_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "consent_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consent_records_expiration_idx": {"name": "consent_records_expiration_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"consent_records_user_id_users_id_fk": {"name": "consent_records_user_id_users_id_fk", "tableFrom": "consent_records", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consent_records_student_id_users_id_fk": {"name": "consent_records_student_id_users_id_fk", "tableFrom": "consent_records", "tableTo": "users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consent_records_organization_id_organizations_id_fk": {"name": "consent_records_organization_id_organizations_id_fk", "tableFrom": "consent_records", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consent_records_withdrawn_by_users_id_fk": {"name": "consent_records_withdrawn_by_users_id_fk", "tableFrom": "consent_records", "tableTo": "users", "columnsFrom": ["withdrawn_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.data_access_requests": {"name": "data_access_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "requester_id": {"name": "requester_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "request_type": {"name": "request_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "data_types": {"name": "data_types", "type": "text", "primaryKey": false, "notNull": true}, "legal_basis": {"name": "legal_basis", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "educational_purpose": {"name": "educational_purpose", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'normal'"}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "response_message": {"name": "response_message", "type": "text", "primaryKey": false, "notNull": false}, "data_provided": {"name": "data_provided", "type": "jsonb", "primaryKey": false, "notNull": false}, "delivery_method": {"name": "delivery_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "ferpa_notification_sent": {"name": "ferpa_notification_sent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parental_consent_required": {"name": "parental_consent_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parental_consent_received": {"name": "parental_consent_received", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"data_access_requests_requester_status_idx": {"name": "data_access_requests_requester_status_idx", "columns": [{"expression": "requester_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "data_access_requests_student_status_idx": {"name": "data_access_requests_student_status_idx", "columns": [{"expression": "student_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "data_access_requests_status_due_date_idx": {"name": "data_access_requests_status_due_date_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "due_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"data_access_requests_requester_id_users_id_fk": {"name": "data_access_requests_requester_id_users_id_fk", "tableFrom": "data_access_requests", "tableTo": "users", "columnsFrom": ["requester_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "data_access_requests_student_id_users_id_fk": {"name": "data_access_requests_student_id_users_id_fk", "tableFrom": "data_access_requests", "tableTo": "users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "data_access_requests_organization_id_organizations_id_fk": {"name": "data_access_requests_organization_id_organizations_id_fk", "tableFrom": "data_access_requests", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "data_access_requests_assigned_to_users_id_fk": {"name": "data_access_requests_assigned_to_users_id_fk", "tableFrom": "data_access_requests", "tableTo": "users", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "data_access_requests_reviewed_by_users_id_fk": {"name": "data_access_requests_reviewed_by_users_id_fk", "tableFrom": "data_access_requests", "tableTo": "users", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.data_retention_schedule": {"name": "data_retention_schedule", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "entity_type": {"name": "entity_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "entity_id": {"name": "entity_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "data_classification": {"name": "data_classification", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "retention_period": {"name": "retention_period", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "legal_requirement": {"name": "legal_requirement", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "created_date": {"name": "created_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "retention_start_date": {"name": "retention_start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "scheduled_deletion_date": {"name": "scheduled_deletion_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "hold_reason": {"name": "hold_reason", "type": "text", "primaryKey": false, "notNull": false}, "hold_by": {"name": "hold_by", "type": "uuid", "primaryKey": false, "notNull": false}, "hold_until": {"name": "hold_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_by": {"name": "deleted_by", "type": "uuid", "primaryKey": false, "notNull": false}, "deletion_method": {"name": "deletion_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "deletion_confirmed": {"name": "deletion_confirmed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "archived_at": {"name": "archived_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "archive_location": {"name": "archive_location", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"data_retention_schedule_entity_idx": {"name": "data_retention_schedule_entity_idx", "columns": [{"expression": "entity_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "entity_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "data_retention_schedule_deletion_date_idx": {"name": "data_retention_schedule_deletion_date_idx", "columns": [{"expression": "scheduled_deletion_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "data_retention_schedule_org_status_idx": {"name": "data_retention_schedule_org_status_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"data_retention_schedule_organization_id_organizations_id_fk": {"name": "data_retention_schedule_organization_id_organizations_id_fk", "tableFrom": "data_retention_schedule", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "data_retention_schedule_hold_by_users_id_fk": {"name": "data_retention_schedule_hold_by_users_id_fk", "tableFrom": "data_retention_schedule", "tableTo": "users", "columnsFrom": ["hold_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "data_retention_schedule_deleted_by_users_id_fk": {"name": "data_retention_schedule_deleted_by_users_id_fk", "tableFrom": "data_retention_schedule", "tableTo": "users", "columnsFrom": ["deleted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.security_incidents": {"name": "security_incidents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "incident_id": {"name": "incident_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "severity": {"name": "severity", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "incident_type": {"name": "incident_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "affected_users": {"name": "affected_users", "type": "text", "primaryKey": false, "notNull": false}, "affected_data_types": {"name": "affected_data_types", "type": "text", "primaryKey": false, "notNull": false}, "detected_at": {"name": "detected_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "occurred_at": {"name": "occurred_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "reported_at": {"name": "reported_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "reported_by": {"name": "reported_by", "type": "uuid", "primaryKey": false, "notNull": false}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "response_actions": {"name": "response_actions", "type": "text", "primaryKey": false, "notNull": false}, "data_compromised": {"name": "data_compromised", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "student_data_involved": {"name": "student_data_involved", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "estimated_records_affected": {"name": "estimated_records_affected", "type": "integer", "primaryKey": false, "notNull": false}, "regulatory_notification_required": {"name": "regulatory_notification_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "regulatory_notification_sent": {"name": "regulatory_notification_sent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parent_notification_required": {"name": "parent_notification_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parent_notification_sent": {"name": "parent_notification_sent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'open'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"security_incidents_severity_status_idx": {"name": "security_incidents_severity_status_idx", "columns": [{"expression": "severity", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "security_incidents_org_incident_idx": {"name": "security_incidents_org_incident_idx", "columns": [{"expression": "organization_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "detected_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "security_incidents_student_data_idx": {"name": "security_incidents_student_data_idx", "columns": [{"expression": "student_data_involved", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "detected_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"security_incidents_organization_id_organizations_id_fk": {"name": "security_incidents_organization_id_organizations_id_fk", "tableFrom": "security_incidents", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "security_incidents_reported_by_users_id_fk": {"name": "security_incidents_reported_by_users_id_fk", "tableFrom": "security_incidents", "tableTo": "users", "columnsFrom": ["reported_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "security_incidents_assigned_to_users_id_fk": {"name": "security_incidents_assigned_to_users_id_fk", "tableFrom": "security_incidents", "tableTo": "users", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"security_incidents_incident_id_unique": {"name": "security_incidents_incident_id_unique", "nullsNotDistinct": false, "columns": ["incident_id"]}}}, "public.email_verification_tokens": {"name": "email_verification_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"email_verification_tokens_user_id_users_id_fk": {"name": "email_verification_tokens_user_id_users_id_fk", "tableFrom": "email_verification_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"email_verification_tokens_token_unique": {"name": "email_verification_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}}, "public.parent_child_relations": {"name": "parent_child_relations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "child_id": {"name": "child_id", "type": "uuid", "primaryKey": false, "notNull": true}, "relationship_type": {"name": "relationship_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'parent'"}, "has_educational_rights": {"name": "has_educational_rights", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "can_view_progress": {"name": "can_view_progress", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "can_manage_account": {"name": "can_manage_account", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"parent_child_relations_parent_id_users_id_fk": {"name": "parent_child_relations_parent_id_users_id_fk", "tableFrom": "parent_child_relations", "tableTo": "users", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "parent_child_relations_child_id_users_id_fk": {"name": "parent_child_relations_child_id_users_id_fk", "tableFrom": "parent_child_relations", "tableTo": "users", "columnsFrom": ["child_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "parent_child_relations_created_by_users_id_fk": {"name": "parent_child_relations_created_by_users_id_fk", "tableFrom": "parent_child_relations", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.password_reset_tokens": {"name": "password_reset_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "used_at": {"name": "used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"password_reset_tokens_user_id_users_id_fk": {"name": "password_reset_tokens_user_id_users_id_fk", "tableFrom": "password_reset_tokens", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"password_reset_tokens_token_unique": {"name": "password_reset_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}}, "public.user_sessions": {"name": "user_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "session_token": {"name": "session_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "device_info": {"name": "device_info", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "revoked_at": {"name": "revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revoked_by": {"name": "revoked_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_sessions_user_id_users_id_fk": {"name": "user_sessions_user_id_users_id_fk", "tableFrom": "user_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_sessions_revoked_by_users_id_fk": {"name": "user_sessions_revoked_by_users_id_fk", "tableFrom": "user_sessions", "tableTo": "users", "columnsFrom": ["revoked_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_sessions_session_token_unique": {"name": "user_sessions_session_token_unique", "nullsNotDistinct": false, "columns": ["session_token"]}}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "timestamp", "primaryKey": false, "notNull": false}, "hashed_password": {"name": "hashed_password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'student'"}, "account_type": {"name": "account_type", "type": "account_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'individual'"}, "is_minor": {"name": "is_minor", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parental_consent_given": {"name": "parental_consent_given", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parental_consent_date": {"name": "parental_consent_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "consent_document": {"name": "consent_document", "type": "text", "primaryKey": false, "notNull": false}, "age_group": {"name": "age_group", "type": "age_group", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'elementary'"}, "content_filter_level": {"name": "content_filter_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'strict'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "data_retention_date": {"name": "data_retention_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "ferpa_directory_info": {"name": "ferpa_directory_info", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}}, "public.classroom_enrollments": {"name": "classroom_enrollments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "classroom_id": {"name": "classroom_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "enrolled_at": {"name": "enrolled_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "final_grade": {"name": "final_grade", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"classroom_enrollments_classroom_id_classrooms_id_fk": {"name": "classroom_enrollments_classroom_id_classrooms_id_fk", "tableFrom": "classroom_enrollments", "tableTo": "classrooms", "columnsFrom": ["classroom_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "classroom_enrollments_user_id_users_id_fk": {"name": "classroom_enrollments_user_id_users_id_fk", "tableFrom": "classroom_enrollments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "classroom_enrollments_created_by_users_id_fk": {"name": "classroom_enrollments_created_by_users_id_fk", "tableFrom": "classroom_enrollments", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.classrooms": {"name": "classrooms", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "grade_level": {"name": "grade_level", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "term": {"name": "term", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "settings": {"name": "settings", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"classrooms_organization_id_organizations_id_fk": {"name": "classrooms_organization_id_organizations_id_fk", "tableFrom": "classrooms", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "classrooms_created_by_users_id_fk": {"name": "classrooms_created_by_users_id_fk", "tableFrom": "classrooms", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.organization_invitations": {"name": "organization_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "accepted_by": {"name": "accepted_by", "type": "uuid", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "inviter_name": {"name": "inviter_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"organization_invitations_organization_id_organizations_id_fk": {"name": "organization_invitations_organization_id_organizations_id_fk", "tableFrom": "organization_invitations", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "organization_invitations_accepted_by_users_id_fk": {"name": "organization_invitations_accepted_by_users_id_fk", "tableFrom": "organization_invitations", "tableTo": "users", "columnsFrom": ["accepted_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "organization_invitations_created_by_users_id_fk": {"name": "organization_invitations_created_by_users_id_fk", "tableFrom": "organization_invitations", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organization_invitations_token_unique": {"name": "organization_invitations_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}}, "public.organization_memberships": {"name": "organization_memberships", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "department": {"name": "department", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "left_at": {"name": "left_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"organization_memberships_organization_id_organizations_id_fk": {"name": "organization_memberships_organization_id_organizations_id_fk", "tableFrom": "organization_memberships", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "organization_memberships_user_id_users_id_fk": {"name": "organization_memberships_user_id_users_id_fk", "tableFrom": "organization_memberships", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "organization_memberships_created_by_users_id_fk": {"name": "organization_memberships_created_by_users_id_fk", "tableFrom": "organization_memberships", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "text", "primaryKey": false, "notNull": false}, "website_url": {"name": "website_url", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "account_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'school'"}, "subdomain": {"name": "subdomain", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "contact_phone": {"name": "contact_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "address_line_1": {"name": "address_line_1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line_2": {"name": "address_line_2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "default": "'US'"}, "ferpa_officer_name": {"name": "ferpa_officer_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ferpa_officer_email": {"name": "ferpa_officer_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ferpa_officer_phone": {"name": "ferpa_officer_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "ferpa_annual_notification": {"name": "ferpa_annual_notification", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "subscription_tier": {"name": "subscription_tier", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'basic'"}, "max_students": {"name": "max_students", "type": "integer", "primaryKey": false, "notNull": false, "default": 100}, "max_storage_gb": {"name": "max_storage_gb", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'10.00'"}, "require_two_factor": {"name": "require_two_factor", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "password_policy": {"name": "password_policy", "type": "text", "primaryKey": false, "notNull": false}, "session_timeout_minutes": {"name": "session_timeout_minutes", "type": "integer", "primaryKey": false, "notNull": false, "default": 60}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "settings": {"name": "settings", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"organizations_created_by_users_id_fk": {"name": "organizations_created_by_users_id_fk", "tableFrom": "organizations", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizations_subdomain_unique": {"name": "organizations_subdomain_unique", "nullsNotDistinct": false, "columns": ["subdomain"]}}}, "public.achievements": {"name": "achievements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "badge_id": {"name": "badge_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "badge_name": {"name": "badge_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "badge_description": {"name": "badge_description", "type": "text", "primaryKey": false, "notNull": false}, "badge_icon": {"name": "badge_icon", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "criteria": {"name": "criteria", "type": "text", "primaryKey": false, "notNull": false}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": false}, "total_questions": {"name": "total_questions", "type": "integer", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "difficulty": {"name": "difficulty", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "is_visible": {"name": "is_visible", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_shared_with_parents": {"name": "is_shared_with_parents", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_shared_with_teachers": {"name": "is_shared_with_teachers", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "unlocked_at": {"name": "unlocked_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"achievements_user_id_users_id_fk": {"name": "achievements_user_id_users_id_fk", "tableFrom": "achievements", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "achievements_organization_id_organizations_id_fk": {"name": "achievements_organization_id_organizations_id_fk", "tableFrom": "achievements", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.chat_sessions": {"name": "chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "material_id": {"name": "material_id", "type": "uuid", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "session_title": {"name": "session_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "context": {"name": "context", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "messages": {"name": "messages", "type": "jsonb", "primaryKey": false, "notNull": true}, "message_count": {"name": "message_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "safety_flags": {"name": "safety_flags", "type": "text", "primaryKey": false, "notNull": false}, "appropriateness_score": {"name": "appropriateness_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "duration_seconds": {"name": "duration_seconds", "type": "integer", "primaryKey": false, "notNull": false}, "tokens_used": {"name": "tokens_used", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ended_at": {"name": "ended_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_sessions_user_id_users_id_fk": {"name": "chat_sessions_user_id_users_id_fk", "tableFrom": "chat_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_sessions_material_id_materials_id_fk": {"name": "chat_sessions_material_id_materials_id_fk", "tableFrom": "chat_sessions", "tableTo": "materials", "columnsFrom": ["material_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chat_sessions_organization_id_organizations_id_fk": {"name": "chat_sessions_organization_id_organizations_id_fk", "tableFrom": "chat_sessions", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.flip_cards": {"name": "flip_cards", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "material_id": {"name": "material_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "cards": {"name": "cards", "type": "jsonb", "primaryKey": false, "notNull": true}, "card_count": {"name": "card_count", "type": "integer", "primaryKey": false, "notNull": true}, "study_count": {"name": "study_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "last_studied_at": {"name": "last_studied_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_shared_with_classroom": {"name": "is_shared_with_classroom", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_shared_with_organization": {"name": "is_shared_with_organization", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"flip_cards_user_id_users_id_fk": {"name": "flip_cards_user_id_users_id_fk", "tableFrom": "flip_cards", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "flip_cards_material_id_materials_id_fk": {"name": "flip_cards_material_id_materials_id_fk", "tableFrom": "flip_cards", "tableTo": "materials", "columnsFrom": ["material_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "flip_cards_organization_id_organizations_id_fk": {"name": "flip_cards_organization_id_organizations_id_fk", "tableFrom": "flip_cards", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.materials": {"name": "materials", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "classroom_id": {"name": "classroom_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "file_mime_type": {"name": "file_mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "safety_status": {"name": "safety_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "safety_score": {"name": "safety_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "safety_review": {"name": "safety_review", "type": "text", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "topics": {"name": "topics", "type": "text", "primaryKey": false, "notNull": false}, "difficulty": {"name": "difficulty", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "age_group": {"name": "age_group", "type": "age_group", "typeSchema": "public", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "share_with_classroom": {"name": "share_with_classroom", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "share_with_organization": {"name": "share_with_organization", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "access_count": {"name": "access_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"materials_user_id_users_id_fk": {"name": "materials_user_id_users_id_fk", "tableFrom": "materials", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "materials_organization_id_organizations_id_fk": {"name": "materials_organization_id_organizations_id_fk", "tableFrom": "materials", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "materials_classroom_id_classrooms_id_fk": {"name": "materials_classroom_id_classrooms_id_fk", "tableFrom": "materials", "tableTo": "classrooms", "columnsFrom": ["classroom_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.quiz_results": {"name": "quiz_results", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "material_id": {"name": "material_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "classroom_id": {"name": "classroom_id", "type": "uuid", "primaryKey": false, "notNull": false}, "quiz_title": {"name": "quiz_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "material_name": {"name": "material_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "total_questions": {"name": "total_questions", "type": "integer", "primaryKey": false, "notNull": true}, "time_spent_seconds": {"name": "time_spent_seconds", "type": "integer", "primaryKey": false, "notNull": false}, "question_results": {"name": "question_results", "type": "jsonb", "primaryKey": false, "notNull": false}, "difficulty_level": {"name": "difficulty_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "topics_tested": {"name": "topics_tested", "type": "text", "primaryKey": false, "notNull": false}, "topics_strong": {"name": "topics_strong", "type": "text", "primaryKey": false, "notNull": false}, "topics_need_work": {"name": "topics_need_work", "type": "text", "primaryKey": false, "notNull": false}, "performance_level": {"name": "performance_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "percentage_score": {"name": "percentage_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "is_shared_with_parents": {"name": "is_shared_with_parents", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_shared_with_teachers": {"name": "is_shared_with_teachers", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"quiz_results_user_id_users_id_fk": {"name": "quiz_results_user_id_users_id_fk", "tableFrom": "quiz_results", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quiz_results_material_id_materials_id_fk": {"name": "quiz_results_material_id_materials_id_fk", "tableFrom": "quiz_results", "tableTo": "materials", "columnsFrom": ["material_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quiz_results_organization_id_organizations_id_fk": {"name": "quiz_results_organization_id_organizations_id_fk", "tableFrom": "quiz_results", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "quiz_results_classroom_id_classrooms_id_fk": {"name": "quiz_results_classroom_id_classrooms_id_fk", "tableFrom": "quiz_results", "tableTo": "classrooms", "columnsFrom": ["classroom_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.student_profiles": {"name": "student_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "student_id": {"name": "student_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "grade_level": {"name": "grade_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "graduation_year": {"name": "graduation_year", "type": "integer", "primaryKey": false, "notNull": false}, "learning_style": {"name": "learning_style", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "accommodations": {"name": "accommodations", "type": "text", "primaryKey": false, "notNull": false}, "special_needs": {"name": "special_needs", "type": "text", "primaryKey": false, "notNull": false}, "preferred_subjects": {"name": "preferred_subjects", "type": "text", "primaryKey": false, "notNull": false}, "difficult_subjects": {"name": "difficult_subjects", "type": "text", "primaryKey": false, "notNull": false}, "learning_goals": {"name": "learning_goals", "type": "text", "primaryKey": false, "notNull": false}, "content_filter_level": {"name": "content_filter_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'strict'"}, "allowed_topics": {"name": "allowed_topics", "type": "text", "primaryKey": false, "notNull": false}, "restricted_topics": {"name": "restricted_topics", "type": "text", "primaryKey": false, "notNull": false}, "max_session_time_minutes": {"name": "max_session_time_minutes", "type": "integer", "primaryKey": false, "notNull": false, "default": 60}, "show_progress_to_parents": {"name": "show_progress_to_parents", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "share_data_with_teachers": {"name": "share_data_with_teachers", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "emergency_contact_name": {"name": "emergency_contact_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "emergency_contact_phone": {"name": "emergency_contact_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "emergency_contact_relation": {"name": "emergency_contact_relation", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"student_profiles_user_id_users_id_fk": {"name": "student_profiles_user_id_users_id_fk", "tableFrom": "student_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_profiles_organization_id_organizations_id_fk": {"name": "student_profiles_organization_id_organizations_id_fk", "tableFrom": "student_profiles", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_profiles_created_by_users_id_fk": {"name": "student_profiles_created_by_users_id_fk", "tableFrom": "student_profiles", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"student_profiles_user_id_unique": {"name": "student_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}}}, "enums": {"public.account_type": {"name": "account_type", "schema": "public", "values": ["individual", "school", "district", "organization"]}, "public.age_group": {"name": "age_group", "schema": "public", "values": ["pre_k", "elementary", "middle", "high", "adult"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["super_admin", "org_admin", "teacher", "parent_guardian", "student"]}}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}