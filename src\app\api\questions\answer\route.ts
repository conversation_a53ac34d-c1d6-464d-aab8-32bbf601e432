import { NextRequest, NextResponse } from 'next/server';
import { answerQuestions, AnswerQuestionsInput } from '@/ai/flows/answer-questions';
import { z } from 'zod';

const RequestSchema = z.object({
  question: z.string().min(1, 'Question is required'),
  context: z.string().min(1, 'Context is required'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedInput = RequestSchema.parse(body);
    
    // Answer question using AI flow
    const result = await answerQuestions(validatedInput);
    
    // Validate answer is not empty
    if (!result.answer || result.answer.trim().length === 0) {
      throw new Error('Empty answer generated');
    }
    
    return NextResponse.json({ 
      success: true, 
      data: result 
    });
    
  } catch (error) {
    console.error('Question answering error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data',
          details: error.errors 
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to answer question' 
      },
      { status: 500 }
    );
  }
}