// src/ai/flows/answer-questions.ts
'use server';
/**
 * @fileOverview A flow that answers questions about a subject matter using provided context.
 *
 * - answerQuestions - A function that takes a question and context, and returns an answer.
 * - AnswerQuestionsInput - The input type for the answerQuestions function.
 * - AnswerQuestionsOutput - The return type for the answerQuestions function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AnswerQuestionsInputSchema = z.object({
  question: z.string().describe('The question to be answered.'),
  context: z.string().describe('The context to use when answering the question.'),
});
export type AnswerQuestionsInput = z.infer<typeof AnswerQuestionsInputSchema>;

const AnswerQuestionsOutputSchema = z.object({
  answer: z.string().describe('The answer to the question.'),
});
export type AnswerQuestionsOutput = z.infer<typeof AnswerQuestionsOutputSchema>;

export async function answerQuestions(input: AnswerQuestionsInput): Promise<AnswerQuestionsOutput> {
  try {
    const result = await answerQuestionsFlow(input);
    
    // Validate output
    if (!result.answer || result.answer.trim().length === 0) {
      throw new Error('Generated answer is empty');
    }
    
    return result;
  } catch (error) {
    console.error('Answer generation error:', error);
    throw new Error(`Failed to generate answer: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

const prompt = ai.definePrompt({
  name: 'answerQuestionsPrompt',
  input: {schema: AnswerQuestionsInputSchema},
  output: {schema: AnswerQuestionsOutputSchema},
  prompt: `You are an AI assistant that answers questions based on the provided context.\n\nContext:\n{{context}}\n\nQuestion: {{question}}\n\nAnswer:`, // Keep the 'Answer:' at the end so the model knows what to generate
});

const answerQuestionsFlow = ai.defineFlow(
  {
    name: 'answerQuestionsFlow',
    inputSchema: AnswerQuestionsInputSchema,
    outputSchema: AnswerQuestionsOutputSchema,
  },
  async input => {
    try {
      const {output} = await prompt(input);
      
      if (!output) {
        throw new Error('No output received from AI model');
      }
      
      return output;
    } catch (error) {
      console.error('Answer flow error:', error);
      throw error;
    }
  }
);
