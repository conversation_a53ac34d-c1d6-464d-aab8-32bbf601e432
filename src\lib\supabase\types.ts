// Supabase Database Type Definitions
// This file will be auto-generated by Supabase CLI
// For now, we'll create a basic structure that matches our Drizzle schema

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          email_verified: string | null;
          hashed_password: string | null;
          first_name: string | null;
          last_name: string | null;
          display_name: string | null;
          avatar_url: string | null;
          role: 'super_admin' | 'org_admin' | 'teacher' | 'parent_guardian' | 'student';
          account_type: 'individual' | 'school' | 'district' | 'organization';
          is_minor: boolean | null;
          parental_consent_given: boolean | null;
          parental_consent_date: string | null;
          consent_document: string | null;
          age_group: 'pre_k' | 'elementary' | 'middle' | 'high' | 'adult' | null;
          content_filter_level: string | null;
          is_active: boolean | null;
          is_email_verified: boolean | null;
          last_login: string | null;
          created_at: string;
          updated_at: string;
          created_by: string | null;
          data_retention_date: string | null;
          ferpa_directory_info: boolean | null;
        };
        Insert: {
          id?: string;
          email: string;
          email_verified?: string | null;
          hashed_password?: string | null;
          first_name?: string | null;
          last_name?: string | null;
          display_name?: string | null;
          avatar_url?: string | null;
          role?: 'super_admin' | 'org_admin' | 'teacher' | 'parent_guardian' | 'student';
          account_type?: 'individual' | 'school' | 'district' | 'organization';
          is_minor?: boolean | null;
          parental_consent_given?: boolean | null;
          parental_consent_date?: string | null;
          consent_document?: string | null;
          age_group?: 'pre_k' | 'elementary' | 'middle' | 'high' | 'adult' | null;
          content_filter_level?: string | null;
          is_active?: boolean | null;
          is_email_verified?: boolean | null;
          last_login?: string | null;
          created_at?: string;
          updated_at?: string;
          created_by?: string | null;
          data_retention_date?: string | null;
          ferpa_directory_info?: boolean | null;
        };
        Update: {
          id?: string;
          email?: string;
          email_verified?: string | null;
          hashed_password?: string | null;
          first_name?: string | null;
          last_name?: string | null;
          display_name?: string | null;
          avatar_url?: string | null;
          role?: 'super_admin' | 'org_admin' | 'teacher' | 'parent_guardian' | 'student';
          account_type?: 'individual' | 'school' | 'district' | 'organization';
          is_minor?: boolean | null;
          parental_consent_given?: boolean | null;
          parental_consent_date?: string | null;
          consent_document?: string | null;
          age_group?: 'pre_k' | 'elementary' | 'middle' | 'high' | 'adult' | null;
          content_filter_level?: string | null;
          is_active?: boolean | null;
          is_email_verified?: boolean | null;
          last_login?: string | null;
          created_at?: string;
          updated_at?: string;
          created_by?: string | null;
          data_retention_date?: string | null;
          ferpa_directory_info?: boolean | null;
        };
      };
      organizations: {
        Row: {
          id: string;
          name: string;
          display_name: string | null;
          description: string | null;
          logo_url: string | null;
          website_url: string | null;
          type: 'individual' | 'school' | 'district' | 'organization';
          subdomain: string | null;
          contact_email: string | null;
          contact_phone: string | null;
          address_line_1: string | null;
          address_line_2: string | null;
          city: string | null;
          state: string | null;
          postal_code: string | null;
          country: string | null;
          ferpa_officer_name: string | null;
          ferpa_officer_email: string | null;
          ferpa_officer_phone: string | null;
          ferpa_annual_notification: boolean | null;
          subscription_tier: string | null;
          max_students: number | null;
          max_storage_gb: string | null;
          require_two_factor: boolean | null;
          password_policy: string | null;
          session_timeout_minutes: number | null;
          is_active: boolean | null;
          settings: string | null;
          created_at: string;
          updated_at: string;
          created_by: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          display_name?: string | null;
          description?: string | null;
          logo_url?: string | null;
          website_url?: string | null;
          type?: 'individual' | 'school' | 'district' | 'organization';
          subdomain?: string | null;
          contact_email?: string | null;
          contact_phone?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          state?: string | null;
          postal_code?: string | null;
          country?: string | null;
          ferpa_officer_name?: string | null;
          ferpa_officer_email?: string | null;
          ferpa_officer_phone?: string | null;
          ferpa_annual_notification?: boolean | null;
          subscription_tier?: string | null;
          max_students?: number | null;
          max_storage_gb?: string | null;
          require_two_factor?: boolean | null;
          password_policy?: string | null;
          session_timeout_minutes?: number | null;
          is_active?: boolean | null;
          settings?: string | null;
          created_at?: string;
          updated_at?: string;
          created_by?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          display_name?: string | null;
          description?: string | null;
          logo_url?: string | null;
          website_url?: string | null;
          type?: 'individual' | 'school' | 'district' | 'organization';
          subdomain?: string | null;
          contact_email?: string | null;
          contact_phone?: string | null;
          address_line_1?: string | null;
          address_line_2?: string | null;
          city?: string | null;
          state?: string | null;
          postal_code?: string | null;
          country?: string | null;
          ferpa_officer_name?: string | null;
          ferpa_officer_email?: string | null;
          ferpa_officer_phone?: string | null;
          ferpa_annual_notification?: boolean | null;
          subscription_tier?: string | null;
          max_students?: number | null;
          max_storage_gb?: string | null;
          require_two_factor?: boolean | null;
          password_policy?: string | null;
          session_timeout_minutes?: number | null;
          is_active?: boolean | null;
          settings?: string | null;
          created_at?: string;
          updated_at?: string;
          created_by?: string | null;
        };
      };
      // Additional tables will be defined here as needed
    };
    Views: {
      // Views will be defined here
    };
    Functions: {
      // Stored procedures will be defined here
    };
    Enums: {
      user_role: 'super_admin' | 'org_admin' | 'teacher' | 'parent_guardian' | 'student';
      account_type: 'individual' | 'school' | 'district' | 'organization';
      age_group: 'pre_k' | 'elementary' | 'middle' | 'high' | 'adult';
    };
  };
}