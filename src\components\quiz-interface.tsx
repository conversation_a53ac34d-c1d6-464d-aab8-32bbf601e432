'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { generateQuiz } from '@/ai/flows/generate-quiz';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Material, Quiz, Achievement, UserProgress } from '@/lib/types';
import { calculatePoints, checkForNewAchievements, getBadgeById } from '@/lib/gamification';
import { Loader2, Zap, ArrowRight, CheckCircle, XCircle, Trophy, Star, Gift } from 'lucide-react';

interface QuizInterfaceProps {
  selectedMaterial: Material | null;
  onQuizComplete: (score: number, total: number) => void;
  quizResults?: any[];
  achievements?: Achievement[];
  onNewAchievements?: (achievements: Achievement[]) => void;
}

export default function QuizInterface({ 
  selectedMaterial, 
  onQuizComplete, 
  quizResults = [], 
  achievements = [], 
  onNewAchievements 
}: QuizInterfaceProps) {
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswers, setUserAnswers] = useState<string[]>([]);
  const [isFinished, setIsFinished] = useState(false);
  const [score, setScore] = useState(0);
  const [earnedPoints, setEarnedPoints] = useState(0);
  const [newAchievements, setNewAchievements] = useState<Achievement[]>([]);
  const [showAchievements, setShowAchievements] = useState(false);
  const { toast } = useToast();

  const handleGenerateQuiz = async () => {
    if (!selectedMaterial) return;
    setIsLoading(true);
    setQuiz(null);
    setIsFinished(false);
    setCurrentQuestionIndex(0);
    setUserAnswers([]);
    setScore(0);
    
    try {
      const result = await generateQuiz({ material: selectedMaterial.content, numberOfQuestions: 5 });
      const parsedQuiz = JSON.parse(result.quiz);
      setQuiz(parsedQuiz);
    } catch (error) {
      console.error('Error generating quiz:', error);
      toast({
        title: 'Quiz Generation Failed',
        description: 'Could not generate a quiz from the material.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnswerSelect = (value: string) => {
    const newAnswers = [...userAnswers];
    newAnswers[currentQuestionIndex] = value;
    setUserAnswers(newAnswers);
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < quiz!.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      // Finish quiz
      let finalScore = 0;
      quiz!.questions.forEach((q, index) => {
        if (q.correctAnswer === userAnswers[index]) {
          finalScore++;
        }
      });
      
      const points = calculatePoints(finalScore, quiz!.questions.length);
      const simulatedResults = [...quizResults, {
        materialId: selectedMaterial!.id,
        materialName: selectedMaterial!.name,
        score: finalScore,
        total: quiz!.questions.length,
        date: new Date().toISOString()
      }];
      
      const newBadges = checkForNewAchievements(simulatedResults, achievements);
      
      setScore(finalScore);
      setEarnedPoints(points);
      setNewAchievements(newBadges);
      setIsFinished(true);
      
      if (newBadges.length > 0) {
        setShowAchievements(true);
        onNewAchievements?.(newBadges);
      }
      
      onQuizComplete(finalScore, quiz!.questions.length);
    }
  };

  if (!selectedMaterial) {
    return (
      <div className="flex h-full flex-col items-center justify-center rounded-lg border border-dashed bg-card/50 p-8 text-center">
        <h3 className="font-headline text-xl font-semibold">No Material Selected</h3>
        <p className="text-muted-foreground">Please select a material from the 'My Materials' tab to generate a quiz.</p>
      </div>
    );
  }
  
  if (isLoading) {
    return (
      <div className="flex h-64 flex-col items-center justify-center space-y-3 rounded-lg border bg-card/50">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <h3 className="font-headline text-lg">Generating Your Quiz...</h3>
        <p className="text-sm text-muted-foreground">Please wait a moment.</p>
      </div>
    );
  }

  if (isFinished) {
    if (showAchievements && newAchievements.length > 0) {
      return (
        <Card className="w-full text-center shadow-lg achievement-unlock">
          <CardHeader className="bg-gradient-to-r from-yellow-50 via-orange-50 to-red-50 rounded-t-lg">
            <CardTitle className="font-headline text-2xl flex items-center justify-center gap-2 text-rainbow">
              <Gift className="h-8 w-8 text-yellow-500 celebrate-shake" />
              🎊 New Achievements! 🎊
            </CardTitle>
            <CardDescription>You've unlocked new badges!</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {newAchievements.map((achievement) => {
                const badge = getBadgeById(achievement.badgeId);
                if (!badge) return null;
                
                return (
                  <motion.div
                    key={achievement.badgeId}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.5 }}
                    className="flex items-center gap-3 p-4 rounded-lg border bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200"
                  >
                    <span className="text-3xl">{badge.icon}</span>
                    <div className="text-left">
                      <h3 className="font-semibold text-lg">{badge.name}</h3>
                      <p className="text-sm text-muted-foreground">{badge.description}</p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={() => setShowAchievements(false)} className="w-full">
              Continue to Results
            </Button>
          </CardFooter>
        </Card>
      );
    }

    return (
      <Card className="w-full text-center shadow-lg quiz-complete-glow">
        <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50 rounded-t-lg">
          <CardTitle className="font-headline text-2xl celebrate-shake">🎉 Quiz Complete! 🎉</CardTitle>
          <CardDescription>Here's how you did on the "{selectedMaterial.name}" quiz.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <p className="text-5xl font-bold text-primary">{score} <span className="text-2xl font-normal text-muted-foreground">/ {quiz!.questions.length}</span></p>
            <p className="text-lg font-semibold">
              {score / quiz!.questions.length >= 0.8 ? "Excellent Work! 👏" : "Good effort, keep practicing!"}
            </p>
          </div>
          
          <div className="flex items-center justify-center gap-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2">
              <Star className="h-6 w-6 text-yellow-500" />
              <div className="text-center">
                <p className="text-2xl font-bold text-primary">{earnedPoints}</p>
                <p className="text-sm text-muted-foreground">Points Earned</p>
              </div>
            </div>
            <div className="h-8 w-px bg-border"></div>
            <div className="flex items-center gap-2">
              <Trophy className="h-6 w-6 text-purple-500" />
              <div className="text-center">
                <p className="text-2xl font-bold text-primary">{Math.round((score / quiz!.questions.length) * 100)}%</p>
                <p className="text-sm text-muted-foreground">Accuracy</p>
              </div>
            </div>
          </div>
          
          {newAchievements.length > 0 && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-green-600">🎉 New badges unlocked!</p>
              <div className="flex flex-wrap justify-center gap-2">
                {newAchievements.map((achievement) => {
                  const badge = getBadgeById(achievement.badgeId);
                  return badge ? (
                    <Badge key={achievement.badgeId} variant="secondary" className="text-sm">
                      {badge.icon} {badge.name}
                    </Badge>
                  ) : null;
                })}
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={handleGenerateQuiz} className="w-full">
            Take Another Quiz
          </Button>
        </CardFooter>
      </Card>
    );
  }

  if (quiz) {
    const question = quiz.questions[currentQuestionIndex];
    return (
      <Card className="w-full shadow-lg">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuestionIndex}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3 }}
          >
            <CardHeader>
              <CardTitle className="font-headline text-xl leading-relaxed">{question.question}</CardTitle>
              <CardDescription>Question {currentQuestionIndex + 1} of {quiz.questions.length}</CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup value={userAnswers[currentQuestionIndex]} onValueChange={handleAnswerSelect} className="space-y-3">
                {question.options.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2 rounded-md border p-3 hover:bg-muted/50 has-[:checked]:bg-accent/20 has-[:checked]:border-accent">
                    <RadioGroupItem value={option} id={`q${currentQuestionIndex}-o${index}`} />
                    <Label htmlFor={`q${currentQuestionIndex}-o${index}`} className="flex-1 cursor-pointer">{option}</Label>
                  </div>
                ))}
              </RadioGroup>
            </CardContent>
          </motion.div>
        </AnimatePresence>
        <CardFooter className="flex-col items-stretch space-y-4">
          <Progress value={((currentQuestionIndex + 1) / quiz.questions.length) * 100} className="h-2" />
          <Button onClick={handleNextQuestion} disabled={!userAnswers[currentQuestionIndex]} className="w-full bg-accent hover:bg-accent/90">
            {currentQuestionIndex < quiz.questions.length - 1 ? 'Next Question' : 'Finish Quiz'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center rounded-lg border border-dashed bg-card/50 p-8 text-center">
      <Zap className="mb-4 h-12 w-12 text-primary" />
      <h3 className="font-headline text-xl font-semibold">Ready to Test Your Knowledge?</h3>
      <p className="mb-6 text-muted-foreground">Generate a quiz based on "{selectedMaterial.name}".</p>
      <Button onClick={handleGenerateQuiz} disabled={isLoading} className="bg-accent hover:bg-accent/90">
        <Zap className="mr-2 h-4 w-4" />
        Generate Quiz
      </Button>
    </div>
  );
}
