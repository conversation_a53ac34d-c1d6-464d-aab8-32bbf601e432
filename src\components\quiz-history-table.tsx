'use client';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { QuizResult } from '@/lib/types';
import { Badge } from '@/components/ui/badge';

interface QuizHistoryTableProps {
  results: QuizResult[];
}

export default function QuizHistoryTable({ results }: QuizHistoryTableProps) {
  const getBadgeVariant = (score: number, total: number): 'success' | 'warning' | 'destructive' => {
    const percentage = (score / total) * 100;
    if (percentage >= 80) return 'success';
    if (percentage >= 50) return 'warning';
    return 'destructive';
  };

  const BadgeWrapper = ({ variant, ...props }: { variant: 'success' | 'warning' | 'destructive'; [key: string]: any }) => {
    const classNames: Record<'success' | 'warning' | 'destructive', string> = {
      success: 'bg-green-100 text-green-800 border-green-200',
      warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      destructive: 'bg-red-100 text-red-800 border-red-200',
    };
    return <Badge className={classNames[variant]} {...props} />;
  }

  if (results.length === 0) {
    return (
      <Card className="border-dashed shadow-none">
        <CardHeader>
          <CardTitle>No Quiz History</CardTitle>
          <CardDescription>
            You haven't completed any quizzes yet. Generate and take a quiz to see your history here.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quiz Performance</CardTitle>
        <CardDescription>A log of all your past quiz attempts.</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Material</TableHead>
              <TableHead className="text-center">Score</TableHead>
              <TableHead className="text-right">Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {results.map((result, index) => (
              <TableRow key={index}>
                <TableCell className="font-medium">{result.materialName}</TableCell>
                <TableCell className="text-center">
                  <BadgeWrapper variant={getBadgeVariant(result.score, result.total)}>
                    {result.score} / {result.total}
                  </BadgeWrapper>
                </TableCell>
                <TableCell className="text-right">{new Date(result.date).toLocaleDateString()}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
