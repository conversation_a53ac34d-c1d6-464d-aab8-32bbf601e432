import { pgTable, uuid, varchar, timestamp, integer, decimal, jsonb, index, text, boolean } from 'drizzle-orm/pg-core';
import { users } from './auth';
import { organizations, classrooms } from './organizations';

// Learning analytics and progress tracking
export const learningAnalytics = pgTable('learning_analytics', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id),
  classroomId: uuid('classroom_id').references(() => classrooms.id),
  
  // Analytics period
  analyticsDate: timestamp('analytics_date').notNull(),
  periodType: varchar('period_type', { length: 20 }).notNull(), // daily, weekly, monthly
  
  // Activity metrics
  sessionsCount: integer('sessions_count').default(0),
  totalTimeSpent: integer('total_time_spent_minutes').default(0),
  averageSessionTime: decimal('average_session_time_minutes', { precision: 8, scale: 2 }),
  
  // Learning progress
  quizzesCompleted: integer('quizzes_completed').default(0),
  averageQuizScore: decimal('average_quiz_score', { precision: 5, scale: 2 }),
  topicsStudied: integer('topics_studied').default(0),
  materialsCreated: integer('materials_created').default(0),
  
  // Performance trends
  performanceImprovement: decimal('performance_improvement', { precision: 5, scale: 2 }),
  consistencyScore: decimal('consistency_score', { precision: 5, scale: 2 }),
  engagementScore: decimal('engagement_score', { precision: 5, scale: 2 }),
  
  // Subject-specific analytics
  subjectPerformance: jsonb('subject_performance'), // {subject: score} mapping
  topicsStrengths: jsonb('topics_strengths'), // Array of strong topics
  topicsWeaknesses: jsonb('topics_weaknesses'), // Array of topics needing work
  
  // Behavioral patterns
  preferredStudyTime: varchar('preferred_study_time', { length: 20 }), // morning, afternoon, evening
  averageQuestionsPerQuiz: decimal('average_questions_per_quiz', { precision: 5, scale: 2 }),
  retryPattern: jsonb('retry_pattern'), // How often students retry quizzes
  
  // Achievement tracking
  badgesEarned: integer('badges_earned').default(0),
  milestonesReached: integer('milestones_reached').default(0),
  streakDays: integer('streak_days').default(0),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  userDateIdx: index('learning_analytics_user_date_idx').on(table.userId, table.analyticsDate),
  orgDateIdx: index('learning_analytics_org_date_idx').on(table.organizationId, table.analyticsDate),
}));

// System usage analytics (aggregated, anonymized data)
export const usageAnalytics = pgTable('usage_analytics', {
  id: uuid('id').primaryKey().defaultRandom(),
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Time period
  analyticsDate: timestamp('analytics_date').notNull(),
  periodType: varchar('period_type', { length: 20 }).notNull(), // daily, weekly, monthly
  
  // User activity metrics
  activeUsers: integer('active_users').default(0),
  newUsers: integer('new_users').default(0),
  totalSessions: integer('total_sessions').default(0),
  averageSessionDuration: decimal('average_session_duration_minutes', { precision: 8, scale: 2 }),
  
  // Content metrics
  quizzesGenerated: integer('quizzes_generated').default(0),
  flipCardsGenerated: integer('flip_cards_generated').default(0),
  materialsUploaded: integer('materials_uploaded').default(0),
  chatMessagesExchanged: integer('chat_messages_exchanged').default(0),
  
  // Performance metrics
  averageQuizScore: decimal('average_quiz_score', { precision: 5, scale: 2 }),
  completionRate: decimal('completion_rate', { precision: 5, scale: 2 }),
  
  // Feature usage
  featureUsage: jsonb('feature_usage'), // {feature: usage_count} mapping
  popularTopics: jsonb('popular_topics'), // Array of most studied topics
  popularSubjects: jsonb('popular_subjects'), // Array of most studied subjects
  
  // Safety metrics
  contentFlagged: integer('content_flagged').default(0),
  safetyViolations: integer('safety_violations').default(0),
  averageSafetyScore: decimal('average_safety_score', { precision: 5, scale: 2 }),
  
  // Technical metrics
  apiCalls: integer('api_calls').default(0),
  errorRate: decimal('error_rate', { precision: 5, scale: 4 }),
  averageResponseTime: decimal('average_response_time_ms', { precision: 8, scale: 2 }),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  dateOrgIdx: index('usage_analytics_date_org_idx').on(table.analyticsDate, table.organizationId),
}));

// Content performance analytics
export const contentAnalytics = pgTable('content_analytics', {
  id: uuid('id').primaryKey().defaultRandom(),
  materialId: uuid('material_id').notNull(), // Not FK to handle deleted materials
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Content metadata
  materialName: varchar('material_name', { length: 255 }).notNull(),
  materialType: varchar('material_type', { length: 50 }).notNull(),
  subject: varchar('subject', { length: 100 }),
  ageGroup: varchar('age_group', { length: 20 }),
  
  // Analytics period
  analyticsDate: timestamp('analytics_date').notNull(),
  periodType: varchar('period_type', { length: 20 }).notNull(),
  
  // Usage metrics
  viewCount: integer('view_count').default(0),
  quizCount: integer('quiz_count').default(0),
  averageQuizScore: decimal('average_quiz_score', { precision: 5, scale: 2 }),
  flipCardGenerations: integer('flip_card_generations').default(0),
  
  // Performance metrics
  difficulty: decimal('difficulty_score', { precision: 5, scale: 2 }), // Calculated from quiz results
  engagementScore: decimal('engagement_score', { precision: 5, scale: 2 }),
  educationalValue: decimal('educational_value', { precision: 5, scale: 2 }),
  
  // User feedback (aggregated)
  positiveReactions: integer('positive_reactions').default(0),
  negativeReactions: integer('negative_reactions').default(0),
  
  // Quality metrics
  safetyScore: decimal('safety_score', { precision: 5, scale: 2 }),
  appropriatenessScore: decimal('appropriateness_score', { precision: 5, scale: 2 }),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  materialDateIdx: index('content_analytics_material_date_idx').on(table.materialId, table.analyticsDate),
  subjectDateIdx: index('content_analytics_subject_date_idx').on(table.subject, table.analyticsDate),
}));

// Real-time activity tracking (for live dashboards)
export const activityLogs = pgTable('activity_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id),
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Activity details
  activityType: varchar('activity_type', { length: 50 }).notNull(), // login, quiz, chat, material_upload, etc.
  activityName: varchar('activity_name', { length: 255 }),
  description: text('description'),
  
  // Context
  entityType: varchar('entity_type', { length: 50 }), // material, quiz, chat, etc.
  entityId: uuid('entity_id'),
  
  // Metadata
  metadata: jsonb('metadata'), // Additional activity-specific data
  
  // Performance tracking
  duration: integer('duration_seconds'),
  success: boolean('success').default(true),
  errorMessage: text('error_message'),
  
  // Device and session info
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  sessionId: uuid('session_id'),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  userActivityIdx: index('activity_logs_user_activity_idx').on(table.userId, table.activityType, table.createdAt),
  orgActivityIdx: index('activity_logs_org_activity_idx').on(table.organizationId, table.activityType, table.createdAt),
  typeTimeIdx: index('activity_logs_type_time_idx').on(table.activityType, table.createdAt),
}));

// Performance metrics for system monitoring
export const performanceMetrics = pgTable('performance_metrics', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Metric identification
  metricName: varchar('metric_name', { length: 100 }).notNull(),
  metricType: varchar('metric_type', { length: 50 }).notNull(), // response_time, error_rate, throughput, etc.
  
  // Context
  endpoint: varchar('endpoint', { length: 255 }),
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Metric values
  value: decimal('value', { precision: 12, scale: 4 }).notNull(),
  unit: varchar('unit', { length: 20 }), // ms, percent, count, etc.
  
  // Time series data
  timestamp: timestamp('timestamp').notNull(),
  timeWindow: varchar('time_window', { length: 20 }), // 1m, 5m, 1h, etc.
  
  // Additional metadata
  tags: jsonb('tags'), // Additional categorization tags
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  metricTimeIdx: index('performance_metrics_metric_time_idx').on(table.metricName, table.timestamp),
  endpointTimeIdx: index('performance_metrics_endpoint_time_idx').on(table.endpoint, table.timestamp),
}));