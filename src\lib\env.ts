import { z } from 'zod';

const envSchema = z.object({
  GOOGLE_GENAI_API_KEY: z.string().min(1, 'Google GenAI API key is required'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Safety and Content Filtering
  CONTENT_SAFETY_STRICT_MODE: z.string().optional().transform(val => val === 'true'),
  DEFAULT_AGE_GROUP: z.enum(['elementary', 'middle', 'high', 'adult']).default('middle'),
  ENABLE_CONTENT_FILTERING: z.string().optional().transform(val => val !== 'false'),
  
  // Compliance
  REQUIRE_PARENTAL_CONSENT: z.string().optional().transform(val => val === 'true'),
  DATA_RETENTION_DAYS: z.string().optional().transform(val => parseInt(val || '365')),
  
  // Optional: External safety APIs
  GOOGLE_PERSPECTIVE_API_KEY: z.string().optional(),
});

type Env = z.infer<typeof envSchema>;

function validateEnv(): Env {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => err.path.join('.')).join(', ');
      throw new Error(`Missing or invalid environment variables: ${missingVars}`);
    }
    throw error;
  }
}

export const env = validateEnv();