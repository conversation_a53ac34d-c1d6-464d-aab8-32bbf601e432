'use server';

/**
 * @fileOverview Generates a quiz from uploaded materials.
 *
 * - generateQuiz - A function that generates a quiz from provided learning materials.
 * - GenerateQuizInput - The input type for the generateQuiz function.
 * - GenerateQuizOutput - The return type for the generateQuiz function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateQuizInputSchema = z.object({
  material: z.string().describe('The learning material to generate a quiz from.'),
  numberOfQuestions: z.number().min(1).max(20).default(5).describe('The number of questions to generate for the quiz.'),
});
export type GenerateQuizInput = z.infer<typeof GenerateQuizInputSchema>;

const GenerateQuizOutputSchema = z.object({
  quiz: z.string().describe('The generated quiz in JSON format.'),
});
export type GenerateQuizOutput = z.infer<typeof GenerateQuizOutputSchema>;

export async function generateQuiz(input: GenerateQuizInput): Promise<GenerateQuizOutput> {
  try {
    const result = await generateQuizFlow(input);
    
    // Validate quiz format
    try {
      const parsed = JSON.parse(result.quiz);
      if (!parsed.questions || !Array.isArray(parsed.questions)) {
        throw new Error('Invalid quiz structure: missing questions array');
      }
    } catch (parseError) {
      throw new Error('Generated quiz is not valid JSON');
    }
    
    return result;
  } catch (error) {
    console.error('Quiz generation error:', error);
    throw new Error(`Failed to generate quiz: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

const generateQuizPrompt = ai.definePrompt({
  name: 'generateQuizPrompt',
  input: {schema: GenerateQuizInputSchema},
  output: {schema: GenerateQuizOutputSchema},
  prompt: `You are a quiz generator. You will receive learning material and generate a quiz based on the material.

  The quiz should be in JSON format and have the following structure:
  {
    "questions": [
      {
        "question": "The question text",
        "options": [
          "Option 1",
          "Option 2",
          "Option 3",
          "Option 4"
        ],
        "correctAnswer": "The correct answer text"
      }
    ]
  }

  Make sure that the correct answer is one of the options.

  Generate {{numberOfQuestions}} questions.

  Material: {{{material}}}
  `,
});

const generateQuizFlow = ai.defineFlow(
  {
    name: 'generateQuizFlow',
    inputSchema: GenerateQuizInputSchema,
    outputSchema: GenerateQuizOutputSchema,
  },
  async input => {
    try {
      const {output} = await generateQuizPrompt(input);
      
      if (!output) {
        throw new Error('No output received from AI model');
      }
      
      return output;
    } catch (error) {
      console.error('Quiz generation flow error:', error);
      throw error;
    }
  }
);
