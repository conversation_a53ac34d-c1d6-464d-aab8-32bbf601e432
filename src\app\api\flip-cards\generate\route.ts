import {NextRequest, NextResponse} from 'next/server';
import {
	generateFlipCards,
	GenerateFlipCardsInput,
} from '@/ai/flows/generate-flip-cards';
import {validateAIResponse} from '@/lib/ai-response-validator';
import {contentSafetyFilter, ContentSafetyError} from '@/lib/content-safety';
import {z} from 'zod';

const RequestSchema = z.object({
	material: z.string().min(1, 'Material content is required'),
	numberOfCards: z.number().int().min(1).max(20).default(10),
	ageGroup: z.enum(['elementary', 'middle', 'high', 'adult']).default('middle'),
});

export async function POST(request: NextRequest) {
	let validatedInput: z.infer<typeof RequestSchema> | null = null;

	try {
		const body = await request.json();

		// Validate input schema
		validatedInput = RequestSchema.parse(body);

		// Step 1: Content safety check on input material
		contentSafetyFilter.updateConfig({
			strictMode: true,
			ageGroup: validatedInput.ageGroup,
			educationalContext: true,
		});

		const inputSafetyResult = await contentSafetyFilter.checkContent(
			validatedInput.material
		);
		if (!inputSafetyResult.isAppropriate) {
			throw new ContentSafetyError(
				'Input material contains inappropriate content for flip cards',
				inputSafetyResult.violations,
				inputSafetyResult.riskLevel
			);
		}

		// Step 2: Generate flip cards using AI flow
		const result = await generateFlipCards({
			material: validatedInput.material,
			numberOfCards: validatedInput.numberOfCards,
		});

		// Step 3: Validate that cards array exists and has content
		if (!result.cards || result.cards.length === 0) {
			throw new Error('No flip cards were generated');
		}

		// Step 4: Validate AI response for safety (convert cards array to JSON for validation)
		const cardsJson = JSON.stringify({cards: result.cards});
		const outputValidation = await validateAIResponse(
			cardsJson,
			'flashcard',
			validatedInput.ageGroup
		);

		if (!outputValidation.isValid) {
			console.warn(
				'Flip cards failed safety validation:',
				outputValidation.issues
			);

			// For critical safety issues, block the response
			if (
				outputValidation.issues.some(
					(issue) => issue.includes('inappropriate') || issue.includes('safety')
				)
			) {
				throw new ContentSafetyError(
					'Generated flip cards contain inappropriate content',
					outputValidation.issues,
					'high'
				);
			}
		}

		return NextResponse.json({
			success: true,
			data: result,
			safetyInfo: {
				inputSafetyScore: inputSafetyResult.confidence,
				outputSafetyScore: outputValidation.safetyScore,
				educationalValue: outputValidation.educationalValue,
				ageAppropriate: outputValidation.ageAppropriate,
			},
		});
	} catch (error) {
		console.error('Flip cards generation error:', error);

		if (error instanceof ContentSafetyError) {
			return NextResponse.json(
				{
					success: false,
					error: error.message,
					violations: error.violations,
					riskLevel: error.riskLevel,
					safetyInfo: {
						message:
							'Content did not meet safety guidelines for educational use',
						ageGroup: validatedInput?.ageGroup || 'middle',
					},
				},
				{status: 400}
			);
		}

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Invalid input data',
					details: error.errors,
				},
				{status: 400}
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to generate flip cards',
			},
			{status: 500}
		);
	}
}
