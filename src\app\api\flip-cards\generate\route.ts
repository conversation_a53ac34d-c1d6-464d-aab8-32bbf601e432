import { NextRequest, NextResponse } from 'next/server';
import { generateFlipCards, GenerateFlipCardsInput } from '@/ai/flows/generate-flip-cards';
import { z } from 'zod';

const RequestSchema = z.object({
  material: z.string().min(1, 'Material content is required'),
  numberOfCards: z.number().int().min(1).max(20).default(10),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedInput = RequestSchema.parse(body);
    
    // Generate flip cards using AI flow
    const result = await generateFlipCards(validatedInput);
    
    // Validate AI output is valid JSON
    try {
      JSON.parse(result.cards);
    } catch {
      throw new Error('Invalid flip cards format generated');
    }
    
    return NextResponse.json({ 
      success: true, 
      data: result 
    });
    
  } catch (error) {
    console.error('Flip cards generation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data',
          details: error.errors 
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate flip cards' 
      },
      { status: 500 }
    );
  }
}