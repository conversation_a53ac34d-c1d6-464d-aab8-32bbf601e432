# **App Name**: ScholarAI

## Core Features:

- AI Chat: AI Chat Interface: Allows users to ask subject-specific questions and receive real-time explanations, using an LLM API tool to retrieve relevant information from uploaded materials.
- Quiz Generation: AI-Powered Quiz Generation: Generates practice quizzes from the uploaded course materials to reinforce learning, using the LLM API.
- Quiz Display and Score Tracking: Display generated quizzes and track scores in local storage.
- Material Upload: Simple file upload for integrated course materials in common formats (.pdf, .txt).
- Authentication: User authentication to ensure the safety of personal materials.
- Learning History: Dashboard to display the history of past learning sessions and track quiz performance.

## Style Guidelines:

- Scholarly Blue (#4285F4) will be used for key branding elements and to convey trust and intelligence.
- A very light, desaturated blue (#E8F0FE) will serve as the main background, ensuring it is easy on the eyes for extended reading and study sessions.
- A calm Teal (#00BCD4) will be used for interactive elements like buttons, links, and highlights to guide the user's attention.
- Body text: 'PT Sans', a humanist sans-serif to provide modern, easily readable text. Headings: 'Space Grotesk', a geometric sans-serif which projects a computerized and techy vibe.
- Use minimalist line icons to represent subjects, features, and actions.
- Responsive design to ensure usability across devices.
- Subtle transitions and animations to enhance user experience.