interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  details?: any;
}

class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  
  const response = await fetch(`/api${url}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  const result: ApiResponse<T> = await response.json();

  if (!result.success) {
    throw new ApiError(
      result.error || 'API request failed',
      response.status,
      result.details
    );
  }

  if (!result.data) {
    throw new ApiError('No data received from API', response.status);
  }

  return result.data;
}

export const apiClient = {
  generateQuiz: async (material: string, numberOfQuestions = 5) => {
    return apiRequest<{ quiz: string }>('/quiz/generate', {
      method: 'POST',
      body: JSON.stringify({ material, numberOfQuestions }),
    });
  },

  generateFlipCards: async (material: string, numberOfCards = 10) => {
    return apiRequest<{ cards: Array<{ term: string; definition: string }> }>('/flip-cards/generate', {
      method: 'POST',
      body: JSON.stringify({ material, numberOfCards }),
    });
  },

  answerQuestion: async (question: string, context: string) => {
    return apiRequest<{ answer: string }>('/questions/answer', {
      method: 'POST',
      body: JSON.stringify({ question, context }),
    });
  },
};

export { ApiError };