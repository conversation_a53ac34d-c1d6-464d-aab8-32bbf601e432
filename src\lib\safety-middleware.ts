import {NextRequest, NextResponse} from 'next/server';
import {ContentSafetyFilter, ContentSafetyError} from './content-safety';
import {AIResponseValidator, validateAIResponse} from './ai-response-validator';
import {z} from 'zod';

// Safety middleware types
export interface SafetyConfig {
	enableInputFiltering: boolean;
	enableOutputFiltering: boolean;
	ageGroup: 'elementary' | 'middle' | 'high' | 'adult';
	strictMode: boolean;
	logViolations: boolean;
	blockOnViolation: boolean;
}

export interface SafetyMiddlewareOptions {
	context: 'quiz' | 'chat' | 'flashcard' | 'general';
	inputFields: string[];
	outputField?: string;
	config?: Partial<SafetyConfig>;
}

// Request validation schema
const SafeRequestSchema = z.object({
	material: z.string().optional(),
	question: z.string().optional(),
	context: z.string().optional(),
	content: z.string().optional(),
	text: z.string().optional(),
});

// Default safety configuration
const DEFAULT_SAFETY_CONFIG: SafetyConfig = {
	enableInputFiltering: true,
	enableOutputFiltering: true,
	ageGroup: 'middle',
	strictMode: true,
	logViolations: true,
	blockOnViolation: true,
};

export class SafetyMiddleware {
	private config: SafetyConfig;
	private contentFilter: ContentSafetyFilter;
	private responseValidator: AIResponseValidator;

	constructor(config: Partial<SafetyConfig> = {}) {
		this.config = {...DEFAULT_SAFETY_CONFIG, ...config};
		this.contentFilter = new ContentSafetyFilter({
			strictMode: this.config.strictMode,
			ageGroup: this.config.ageGroup,
			educationalContext: true,
		});
		this.responseValidator = new AIResponseValidator({
			context: 'general',
			ageGroup: this.config.ageGroup,
			strictMode: this.config.strictMode,
			requireEducationalValue: true,
		});
	}

	/**
	 * Main middleware function for API route protection
	 */
	createMiddleware(options: SafetyMiddlewareOptions) {
		return async (
			request: NextRequest,
			handler: (req: NextRequest) => Promise<NextResponse>
		) => {
			try {
				// 1. Input validation and filtering
				if (this.config.enableInputFiltering) {
					const inputValidation = await this.validateInput(request, options);
					if (!inputValidation.isValid) {
						return this.createErrorResponse(
							'Input content violates safety guidelines',
							inputValidation.violations,
							400
						);
					}
				}

				// 2. Execute the original handler
				const response = await handler(request);

				// 3. Output validation and filtering
				if (this.config.enableOutputFiltering && response.ok) {
					const outputValidation = await this.validateOutput(response, options);
					if (!outputValidation.isValid) {
						return this.createErrorResponse(
							'Generated content violates safety guidelines',
							outputValidation.violations,
							500
						);
					}
				}

				// 4. Add safety headers
				return this.addSafetyHeaders(response);
			} catch (error) {
				console.error('Safety middleware error:', error);

				if (error instanceof ContentSafetyError) {
					return this.createErrorResponse(error.message, error.violations, 400);
				}

				return this.createErrorResponse(
					'Safety validation failed',
					['safety-check-error'],
					500
				);
			}
		};
	}

	/**
	 * Validate input content from request
	 */
	private async validateInput(
		request: NextRequest,
		options: SafetyMiddlewareOptions
	): Promise<{isValid: boolean; violations: string[]}> {
		try {
			const body = await request.json();
			const validatedBody = SafeRequestSchema.parse(body);
			const violations: string[] = [];

			// Check each specified input field
			for (const field of options.inputFields) {
				const content = validatedBody[field as keyof typeof validatedBody];
				if (typeof content === 'string' && content.length > 0) {
					const safetyResult = await this.contentFilter.checkContent(content);

					if (!safetyResult.isAppropriate) {
						violations.push(`${field}: ${safetyResult.violations.join(', ')}`);

						if (this.config.logViolations) {
							await this.logSafetyViolation(
								'input',
								field,
								content,
								safetyResult.violations
							);
						}
					}
				}
			}

			return {
				isValid: violations.length === 0 || !this.config.blockOnViolation,
				violations,
			};
		} catch (error) {
			console.error('Input validation error:', error);
			return {
				isValid: false,
				violations: ['input-validation-error'],
			};
		}
	}

	/**
	 * Validate output content from response
	 */
	private async validateOutput(
		response: NextResponse,
		options: SafetyMiddlewareOptions
	): Promise<{isValid: boolean; violations: string[]}> {
		try {
			// Clone response to read body without consuming it
			const responseClone = response.clone();
			const body = await responseClone.json();

			if (!body.success || !body.data) {
				return {isValid: true, violations: []}; // No content to validate
			}

			const outputField = options.outputField || 'quiz';
			const content = body.data[outputField];

			if (typeof content === 'string' && content.length > 0) {
				// Update validator context
				this.responseValidator.updateConfig({context: options.context});

				const validationResult = await this.responseValidator.validateResponse(
					content
				);

				if (!validationResult.isValid) {
					if (this.config.logViolations) {
						await this.logSafetyViolation(
							'output',
							outputField,
							content,
							validationResult.issues
						);
					}

					return {
						isValid: false,
						violations: validationResult.issues,
					};
				}
			}

			return {isValid: true, violations: []};
		} catch (error) {
			console.error('Output validation error:', error);
			return {
				isValid: false,
				violations: ['output-validation-error'],
			};
		}
	}

	/**
	 * Create standardized error response
	 */
	private createErrorResponse(
		message: string,
		violations: string[],
		status: number
	): NextResponse {
		return NextResponse.json(
			{
				success: false,
				error: message,
				violations,
				safetyInfo: {
					timestamp: new Date().toISOString(),
					ageGroup: this.config.ageGroup,
					strictMode: this.config.strictMode,
				},
			},
			{status}
		);
	}

	/**
	 * Add safety-related headers to response
	 */
	private addSafetyHeaders(response: NextResponse): NextResponse {
		response.headers.set('X-Content-Safety', 'enabled');
		response.headers.set('X-Age-Group', this.config.ageGroup);
		response.headers.set('X-Safety-Version', '1.0.0');
		return response;
	}

	/**
	 * Log safety violations for monitoring and analysis
	 */
	private async logSafetyViolation(
		type: 'input' | 'output',
		field: string,
		content: string,
		violations: string[]
	): Promise<void> {
		try {
			const logEntry = {
				timestamp: new Date().toISOString(),
				type,
				field,
				contentLength: content.length,
				contentPreview:
					content.substring(0, 100) + (content.length > 100 ? '...' : ''),
				violations,
				ageGroup: this.config.ageGroup,
				strictMode: this.config.strictMode,
				userAgent: '', // Would be populated in actual middleware
				ip: '', // Would be populated in actual middleware
			};

			// In production, send to logging service (Sentry, DataDog, etc.)
			console.warn('Safety violation detected:', logEntry);

			// Store in database for analysis
			// await storeSafetyLog(logEntry);
		} catch (error) {
			console.error('Failed to log safety violation:', error);
		}
	}

	/**
	 * Update middleware configuration
	 */
	updateConfig(newConfig: Partial<SafetyConfig>): void {
		this.config = {...this.config, ...newConfig};
		this.contentFilter.updateConfig({
			strictMode: this.config.strictMode,
			ageGroup: this.config.ageGroup,
			educationalContext: true,
		});
		this.responseValidator.updateConfig({
			ageGroup: this.config.ageGroup,
			strictMode: this.config.strictMode,
			requireEducationalValue: true,
		});
	}

	/**
	 * Get middleware statistics
	 */
	getStats(): SafetyConfig & {version: string} {
		return {
			...this.config,
			version: '1.0.0',
		};
	}
}

// Utility function to create safety middleware for specific contexts
export function createSafetyMiddleware(options: SafetyMiddlewareOptions) {
	const middleware = new SafetyMiddleware(options.config);
	return middleware.createMiddleware(options);
}

// Pre-configured middleware for common use cases
export const quizSafetyMiddleware = createSafetyMiddleware({
	context: 'quiz',
	inputFields: ['material', 'numberOfQuestions'],
	outputField: 'quiz',
	config: {
		enableInputFiltering: true,
		enableOutputFiltering: true,
		ageGroup: 'middle',
		strictMode: true,
	},
});

export const chatSafetyMiddleware = createSafetyMiddleware({
	context: 'chat',
	inputFields: ['question', 'context'],
	outputField: 'answer',
	config: {
		enableInputFiltering: true,
		enableOutputFiltering: true,
		ageGroup: 'middle',
		strictMode: true,
	},
});

export const flashcardSafetyMiddleware = createSafetyMiddleware({
	context: 'flashcard',
	inputFields: ['material', 'numberOfCards'],
	outputField: 'cards',
	config: {
		enableInputFiltering: true,
		enableOutputFiltering: true,
		ageGroup: 'middle',
		strictMode: true,
	},
});

// Higher-order function to wrap API handlers with safety
export function withSafety(
	handler: (req: NextRequest) => Promise<NextResponse>,
	options: SafetyMiddlewareOptions
) {
	const safetyMiddleware = createSafetyMiddleware(options);

	return async (req: NextRequest) => {
		return safetyMiddleware(req, handler);
	};
}

// Export types are already defined as interfaces above
