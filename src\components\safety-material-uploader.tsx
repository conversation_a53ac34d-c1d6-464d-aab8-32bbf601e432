'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { contentSafetyFilter, SafetyResult } from '@/lib/content-safety';
import { 
  FileUp, 
  CheckCircle, 
  XCircle, 
  BookOpen, 
  Type, 
  FileText, 
  Lightbulb, 
  Shield, 
  AlertTriangle,
  ThumbsUp 
} from 'lucide-react';

interface SafetyMaterialUploaderProps {
  onUpload: (name: string, content: string) => void;
  disabled: boolean;
  ageGroup?: 'elementary' | 'middle' | 'high' | 'adult';
  strictMode?: boolean;
}

export default function SafetyMaterialUploader({ 
  onUpload, 
  disabled, 
  ageGroup = 'middle',
  strictMode = true 
}: SafetyMaterialUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [topicInput, setTopicInput] = useState('');
  const [textContent, setTextContent] = useState('');
  const [activeTab, setActiveTab] = useState('topic');
  const [safetyResult, setSafetyResult] = useState<SafetyResult | null>(null);
  const [isCheckingContent, setIsCheckingContent] = useState(false);
  const { toast } = useToast();

  // Configure content filter
  contentSafetyFilter.updateConfig({
    strictMode,
    ageGroup,
    educationalContext: true,
  });

  const checkContentSafety = useCallback(async (content: string, name: string) => {
    if (!content.trim()) return null;
    
    setIsCheckingContent(true);
    try {
      const result = await contentSafetyFilter.checkContent(content);
      setSafetyResult(result);
      
      if (!result.isAppropriate) {
        toast({
          title: 'Content Safety Alert',
          description: `Content may not be appropriate for ${ageGroup} students: ${result.violations.join(', ')}`,
          variant: 'destructive',
        });
        return result;
      }
      
      // Content is safe, proceed with upload
      onUpload(name, content);
      setSafetyResult(null);
      
      toast({
        title: 'Content Approved',
        description: `${name} has been safely added to your materials.`,
        action: <CheckCircle className="text-green-500" />,
      });
      
      return result;
    } catch (error) {
      console.error('Safety check error:', error);
      toast({
        title: 'Safety Check Failed',
        description: 'Unable to verify content safety. Please try again.',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsCheckingContent(false);
    }
  }, [ageGroup, strictMode, onUpload, toast]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (disabled) return;
    setIsUploading(true);
    
    const file = acceptedFiles[0];
    if (file) {
      if (file.type !== 'text/plain' && file.type !== 'application/pdf') {
        toast({
          title: 'Invalid File Type',
          description: 'Please upload a .txt or .pdf file.',
          variant: 'destructive',
        });
        setIsUploading(false);
        return;
      }
      
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast({
          title: 'File Too Large',
          description: 'Please upload files smaller than 5MB for safety processing.',
          variant: 'destructive',
        });
        setIsUploading(false);
        return;
      }

      if (file.type === 'application/pdf') {
        // PDF placeholder with safety notice
        const content = `PDF Document: ${file.name}\n\nThis PDF document has been uploaded for educational use. Content will be reviewed for age-appropriateness and educational value.\n\nFile size: ${(file.size / 1024).toFixed(2)} KB\nType: PDF Document\n\nThe AI can provide general educational assistance based on the document context.`;
        
        await checkContentSafety(content, file.name);
        setIsUploading(false);
      } else {
        // Handle text files with safety check
        const reader = new FileReader();
        reader.onload = async (e) => {
          const content = e.target?.result as string;
          await checkContentSafety(content, file.name);
          setIsUploading(false);
        };
        reader.onerror = () => {
          toast({
            title: 'Upload Failed',
            description: 'There was an error reading the file.',
            variant: 'destructive',
          });
          setIsUploading(false);
        };
        reader.readAsText(file);
      }
    } else {
      setIsUploading(false);
    }
  }, [disabled, checkContentSafety, toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop, 
    multiple: false, 
    disabled: disabled || isCheckingContent,
    accept: {
      'text/plain': ['.txt'],
      'application/pdf': ['.pdf']
    }
  });

  const handleTopicSubmit = async () => {
    if (!topicInput.trim()) {
      toast({
        title: 'Topic Required',
        description: 'Please enter a topic to study.',
        variant: 'destructive',
      });
      return;
    }
    
    const materialName = `Topic: ${topicInput}`;
    const content = `Study Topic: ${topicInput}\n\nThis is a general topic for AI-generated study content. The AI will provide age-appropriate information and study materials based on this topic for ${ageGroup} level students.`;
    
    const result = await checkContentSafety(content, materialName);
    if (result?.isAppropriate) {
      setTopicInput('');
    }
  };

  const handleTextSubmit = async () => {
    if (!textContent.trim()) {
      toast({
        title: 'Content Required',
        description: 'Please enter some text content.',
        variant: 'destructive',
      });
      return;
    }
    
    const materialName = `Text Content: ${textContent.substring(0, 30)}${textContent.length > 30 ? '...' : ''}`;
    
    const result = await checkContentSafety(textContent, materialName);
    if (result?.isAppropriate) {
      setTextContent('');
    }
  };

  const getSafetyBadge = (result: SafetyResult) => {
    if (result.isAppropriate) {
      return (
        <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
          <ThumbsUp className="h-3 w-3 mr-1" />
          Safe for {ageGroup}
        </Badge>
      );
    } else {
      const variant = result.riskLevel === 'high' ? 'destructive' : 'secondary';
      return (
        <Badge variant={variant} className={
          result.riskLevel === 'high' 
            ? 'bg-red-100 text-red-800 border-red-200'
            : 'bg-yellow-100 text-yellow-800 border-yellow-200'
        }>
          <AlertTriangle className="h-3 w-3 mr-1" />
          {result.riskLevel.toUpperCase()} Risk
        </Badge>
      );
    }
  };

  return (
    <div className="space-y-4">
      {/* Safety Status Display */}
      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center gap-2">
          <Shield className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-900">
            Content Safety: {strictMode ? 'Strict Mode' : 'Standard Mode'}
          </span>
          <Badge variant="outline" className="text-xs">
            {ageGroup.charAt(0).toUpperCase() + ageGroup.slice(1)} Level
          </Badge>
        </div>
        {isCheckingContent && (
          <div className="flex items-center gap-2 text-sm text-blue-600">
            <div className="animate-spin rounded-full h-3 w-3 border border-blue-600 border-t-transparent"></div>
            Checking safety...
          </div>
        )}
      </div>

      {/* Safety Alert if content is not appropriate */}
      {safetyResult && !safetyResult.isAppropriate && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="font-medium">Content Safety Issues Detected:</span>
                {getSafetyBadge(safetyResult)}
              </div>
              <ul className="list-disc pl-4 space-y-1">
                {safetyResult.violations.map((violation, index) => (
                  <li key={index} className="text-sm">
                    {violation.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </li>
                ))}
              </ul>
              {safetyResult.suggestions && safetyResult.suggestions.length > 0 && (
                <div className="mt-2">
                  <span className="font-medium text-sm">Suggestions:</span>
                  <ul className="list-disc pl-4 space-y-1">
                    {safetyResult.suggestions.map((suggestion, index) => (
                      <li key={index} className="text-sm">{suggestion}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="topic" className="flex items-center gap-2">
            <Lightbulb className="h-4 w-4" />
            Topic
          </TabsTrigger>
          <TabsTrigger value="text" className="flex items-center gap-2">
            <Type className="h-4 w-4" />
            Text
          </TabsTrigger>
          <TabsTrigger value="file" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            File
          </TabsTrigger>
        </TabsList>

        <TabsContent value="topic" className="mt-4">
          <div className="space-y-4">
            <div className="text-center mb-4">
              <Lightbulb className="h-12 w-12 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-lg">Study a Topic</h3>
              <p className="text-sm text-muted-foreground">
                Enter any educational topic appropriate for {ageGroup} level students
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="topic">What would you like to study?</Label>
              <Input
                id="topic"
                placeholder={
                  ageGroup === 'elementary' 
                    ? "e.g., Animals, Colors, Basic Math..."
                    : ageGroup === 'middle'
                    ? "e.g., Ancient Egypt, Photosynthesis, Fractions..."
                    : "e.g., World War II, Calculus, Literature Analysis..."
                }
                value={topicInput}
                onChange={(e) => setTopicInput(e.target.value)}
                disabled={disabled || isCheckingContent}
                onKeyDown={(e) => e.key === 'Enter' && handleTopicSubmit()}
              />
            </div>
            <Button 
              onClick={handleTopicSubmit} 
              disabled={disabled || !topicInput.trim() || isCheckingContent}
              className="w-full"
            >
              {isCheckingContent ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border border-white border-t-transparent mr-2"></div>
                  Checking Safety...
                </>
              ) : (
                <>
                  <BookOpen className="mr-2 h-4 w-4" />
                  Start Learning
                </>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="text" className="mt-4">
          <div className="space-y-4">
            <div className="text-center mb-4">
              <Type className="h-12 w-12 text-primary mx-auto mb-2" />
              <h3 className="font-semibold text-lg">Paste Text Content</h3>
              <p className="text-sm text-muted-foreground">
                Copy and paste educational content from articles, homework, or notes
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="textcontent">Paste your content here</Label>
              <Textarea
                id="textcontent"
                placeholder="Paste your educational text content here..."
                value={textContent}
                onChange={(e) => setTextContent(e.target.value)}
                disabled={disabled || isCheckingContent}
                className="min-h-[150px]"
              />
              <div className="text-xs text-muted-foreground">
                Content will be automatically checked for age-appropriateness and educational value.
              </div>
            </div>
            <Button 
              onClick={handleTextSubmit} 
              disabled={disabled || !textContent.trim() || isCheckingContent}
              className="w-full"
            >
              {isCheckingContent ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border border-white border-t-transparent mr-2"></div>
                  Checking Safety...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Add Content
                </>
              )}
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="file" className="mt-4">
          <div
            {...getRootProps()}
            className={`flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-8 text-center transition-colors duration-200 ${
              isDragActive ? 'border-primary bg-primary/10' : 'border-border hover:border-primary/50'
            } ${disabled || isCheckingContent ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
          >
            <input {...getInputProps()} />
            <FileUp className="mb-4 h-12 w-12 text-muted-foreground" />
            {isUploading || isCheckingContent ? (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border border-primary border-t-transparent"></div>
                  <p className="font-semibold text-primary">
                    {isCheckingContent ? 'Checking content safety...' : 'Processing file...'}
                  </p>
                </div>
                <p className="text-sm text-muted-foreground">Please wait while we ensure content is appropriate</p>
              </div>
            ) : isDragActive ? (
              <p className="font-semibold text-primary">Drop the file here ...</p>
            ) : (
              <>
                <p className="font-semibold">Drag & drop your .txt or .pdf file here, or click to select</p>
                <p className="text-sm text-muted-foreground">
                  Upload your textbooks, notes, or course materials (max 5MB)
                </p>
                <div className="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
                  <Shield className="h-3 w-3" />
                  All uploads are automatically checked for safety and appropriateness
                </div>
              </>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}