import { Badge, Achievement, UserProgress, QuizResult } from './types';

export const BADGES: Badge[] = [
  {
    id: 'first-quiz',
    name: 'Getting Started',
    description: 'Completed your first quiz',
    icon: '🎯',
    color: 'bg-blue-500',
    requirement: 'Complete 1 quiz'
  },
  {
    id: 'perfect-score',
    name: 'Perfect Score',
    description: 'Got 100% on a quiz',
    icon: '⭐',
    color: 'bg-yellow-500',
    requirement: 'Score 100% on any quiz'
  },
  {
    id: 'quiz-master',
    name: 'Quiz Master',
    description: 'Completed 10 quizzes',
    icon: '🏆',
    color: 'bg-purple-500',
    requirement: 'Complete 10 quizzes'
  },
  {
    id: 'high-achiever',
    name: 'High Achiever',
    description: 'Maintain 80%+ average score',
    icon: '🌟',
    color: 'bg-green-500',
    requirement: 'Maintain 80%+ average score'
  },
  {
    id: 'vocabulary-whiz',
    name: 'Vocabulary Whiz',
    description: 'Excellent performance on vocabulary topics',
    icon: '📚',
    color: 'bg-indigo-500',
    requirement: 'Score 90%+ on vocabulary quizzes'
  },
  {
    id: 'history-buff',
    name: 'History Buff',
    description: 'Excellent performance on history topics',
    icon: '🏛️',
    color: 'bg-red-500',
    requirement: 'Score 90%+ on history quizzes'
  },
  {
    id: 'science-explorer',
    name: 'Science Explorer',
    description: 'Excellent performance on science topics',
    icon: '🔬',
    color: 'bg-cyan-500',
    requirement: 'Score 90%+ on science quizzes'
  },
  {
    id: 'dedicated-learner',
    name: 'Dedicated Learner',
    description: 'Completed 25 quizzes',
    icon: '💎',
    color: 'bg-pink-500',
    requirement: 'Complete 25 quizzes'
  }
];

export function calculatePoints(score: number, total: number): number {
  const percentage = score / total;
  let basePoints = score * 10; // 10 points per correct answer
  
  // Bonus points for high performance
  if (percentage >= 1.0) basePoints += 50; // Perfect score bonus
  else if (percentage >= 0.9) basePoints += 30; // 90%+ bonus
  else if (percentage >= 0.8) basePoints += 20; // 80%+ bonus
  else if (percentage >= 0.7) basePoints += 10; // 70%+ bonus
  
  return basePoints;
}

export function checkForNewAchievements(
  quizResults: QuizResult[],
  currentAchievements: Achievement[]
): Achievement[] {
  const newAchievements: Achievement[] = [];
  const currentBadgeIds = currentAchievements.map(a => a.badgeId);
  
  // First Quiz
  if (quizResults.length >= 1 && !currentBadgeIds.includes('first-quiz')) {
    newAchievements.push({
      badgeId: 'first-quiz',
      unlockedAt: new Date().toISOString(),
      score: quizResults[0].score,
      total: quizResults[0].total
    });
  }
  
  // Perfect Score
  const perfectScores = quizResults.filter(r => r.score === r.total);
  if (perfectScores.length > 0 && !currentBadgeIds.includes('perfect-score')) {
    const latestPerfect = perfectScores[perfectScores.length - 1];
    newAchievements.push({
      badgeId: 'perfect-score',
      unlockedAt: new Date().toISOString(),
      score: latestPerfect.score,
      total: latestPerfect.total
    });
  }
  
  // Quiz Master (10 quizzes)
  if (quizResults.length >= 10 && !currentBadgeIds.includes('quiz-master')) {
    newAchievements.push({
      badgeId: 'quiz-master',
      unlockedAt: new Date().toISOString()
    });
  }
  
  // Dedicated Learner (25 quizzes)
  if (quizResults.length >= 25 && !currentBadgeIds.includes('dedicated-learner')) {
    newAchievements.push({
      badgeId: 'dedicated-learner',
      unlockedAt: new Date().toISOString()
    });
  }
  
  // High Achiever (80%+ average)
  if (quizResults.length >= 5) {
    const averageScore = quizResults.reduce((sum, r) => sum + (r.score / r.total), 0) / quizResults.length;
    if (averageScore >= 0.8 && !currentBadgeIds.includes('high-achiever')) {
      newAchievements.push({
        badgeId: 'high-achiever',
        unlockedAt: new Date().toISOString()
      });
    }
  }
  
  // Subject-specific badges (check material names for keywords)
  const vocabularyQuizzes = quizResults.filter(r => 
    r.materialName.toLowerCase().includes('vocabulary') || 
    r.materialName.toLowerCase().includes('words') ||
    r.materialName.toLowerCase().includes('language')
  );
  
  if (vocabularyQuizzes.length > 0 && !currentBadgeIds.includes('vocabulary-whiz')) {
    const avgVocabScore = vocabularyQuizzes.reduce((sum, r) => sum + (r.score / r.total), 0) / vocabularyQuizzes.length;
    if (avgVocabScore >= 0.9) {
      newAchievements.push({
        badgeId: 'vocabulary-whiz',
        unlockedAt: new Date().toISOString()
      });
    }
  }
  
  const historyQuizzes = quizResults.filter(r => 
    r.materialName.toLowerCase().includes('history') || 
    r.materialName.toLowerCase().includes('historical') ||
    r.materialName.toLowerCase().includes('ancient')
  );
  
  if (historyQuizzes.length > 0 && !currentBadgeIds.includes('history-buff')) {
    const avgHistoryScore = historyQuizzes.reduce((sum, r) => sum + (r.score / r.total), 0) / historyQuizzes.length;
    if (avgHistoryScore >= 0.9) {
      newAchievements.push({
        badgeId: 'history-buff',
        unlockedAt: new Date().toISOString()
      });
    }
  }
  
  const scienceQuizzes = quizResults.filter(r => 
    r.materialName.toLowerCase().includes('science') || 
    r.materialName.toLowerCase().includes('biology') ||
    r.materialName.toLowerCase().includes('chemistry') ||
    r.materialName.toLowerCase().includes('physics') ||
    r.materialName.toLowerCase().includes('photosynthesis')
  );
  
  if (scienceQuizzes.length > 0 && !currentBadgeIds.includes('science-explorer')) {
    const avgScienceScore = scienceQuizzes.reduce((sum, r) => sum + (r.score / r.total), 0) / scienceQuizzes.length;
    if (avgScienceScore >= 0.9) {
      newAchievements.push({
        badgeId: 'science-explorer',
        unlockedAt: new Date().toISOString()
      });
    }
  }
  
  return newAchievements;
}

export function calculateUserProgress(
  quizResults: QuizResult[],
  achievements: Achievement[]
): UserProgress {
  const totalPoints = quizResults.reduce((sum, result) => 
    sum + calculatePoints(result.score, result.total), 0
  );
  
  const averageScore = quizResults.length > 0 
    ? quizResults.reduce((sum, r) => sum + (r.score / r.total), 0) / quizResults.length 
    : 0;
  
  return {
    totalPoints,
    totalQuizzes: quizResults.length,
    averageScore,
    achievements,
    streakDays: 1, // TODO: Implement streak calculation
    lastActiveDate: new Date().toISOString()
  };
}

export function getBadgeById(id: string): Badge | undefined {
  return BADGES.find(badge => badge.id === id);
}