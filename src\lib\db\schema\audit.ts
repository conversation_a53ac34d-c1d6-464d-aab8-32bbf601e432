import { pgTable, uuid, varchar, timestamp, integer, jsonb, index, text, boolean } from 'drizzle-orm/pg-core';
import { users } from './auth';
import { organizations } from './organizations';

// FERPA-compliant audit logging for educational data access
export const auditLogs = pgTable('audit_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Context identification
  userId: uuid('user_id').references(() => users.id), // Who performed the action
  organizationId: uuid('organization_id').references(() => organizations.id),
  sessionId: uuid('session_id'), // Session identifier
  
  // Action details
  action: varchar('action', { length: 100 }).notNull(), // CREATE, READ, UPDATE, DELETE, LOGIN, etc.
  entityType: varchar('entity_type', { length: 50 }).notNull(), // user, student, material, quiz_result, etc.
  entityId: uuid('entity_id'), // ID of the affected entity
  
  // FERPA compliance fields
  studentDataAccessed: boolean('student_data_accessed').default(false), // Was student data involved?
  dataClassification: varchar('data_classification', { length: 50 }), // public, internal, confidential, restricted
  legalBasis: varchar('legal_basis', { length: 100 }), // educational_purpose, parental_consent, legitimate_interest
  
  // Action context
  description: text('description'), // Human-readable description
  reason: text('reason'), // Business justification for the action
  
  // Change tracking
  oldValues: jsonb('old_values'), // Previous state (encrypted for sensitive data)
  newValues: jsonb('new_values'), // New state (encrypted for sensitive data)
  changedFields: text('changed_fields'), // JSON array of field names that changed
  
  // Request metadata
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  method: varchar('method', { length: 10 }), // GET, POST, PUT, DELETE
  endpoint: varchar('endpoint', { length: 500 }), // API endpoint or page
  requestId: uuid('request_id'), // Unique request identifier
  
  // Result information
  success: boolean('success').notNull().default(true),
  errorMessage: text('error_message'),
  httpStatus: varchar('http_status', { length: 10 }),
  
  // Privacy and compliance
  dataRetentionDate: timestamp('data_retention_date'), // When this audit log expires
  sensitiveDataMasked: boolean('sensitive_data_masked').default(true),
  
  // Risk and security
  riskLevel: varchar('risk_level', { length: 20 }).default('low'), // low, medium, high, critical
  securityFlags: text('security_flags'), // JSON array of security concerns
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  userActionIdx: index('audit_logs_user_action_idx').on(table.userId, table.action, table.createdAt),
  entityIdx: index('audit_logs_entity_idx').on(table.entityType, table.entityId, table.createdAt),
  studentDataIdx: index('audit_logs_student_data_idx').on(table.studentDataAccessed, table.createdAt),
  orgDateIdx: index('audit_logs_org_date_idx').on(table.organizationId, table.createdAt),
  riskLevelIdx: index('audit_logs_risk_level_idx').on(table.riskLevel, table.createdAt),
}));

// Data access requests and consent management (FERPA Rights)
export const dataAccessRequests = pgTable('data_access_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Request identification
  requesterId: uuid('requester_id').references(() => users.id).notNull(),
  studentId: uuid('student_id').references(() => users.id), // Student whose data is requested
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Request details
  requestType: varchar('request_type', { length: 50 }).notNull(), // access, correction, deletion, portability
  description: text('description').notNull(),
  dataTypes: text('data_types').notNull(), // JSON array of requested data types
  
  // Legal basis and justification
  legalBasis: varchar('legal_basis', { length: 100 }).notNull(), // FERPA compliance basis
  educationalPurpose: text('educational_purpose'), // Educational justification
  
  // Request status
  status: varchar('status', { length: 20 }).default('pending'), // pending, approved, denied, completed
  priority: varchar('priority', { length: 20 }).default('normal'), // low, normal, high, urgent
  
  // Processing information
  assignedTo: uuid('assigned_to').references(() => users.id), // Staff member handling request
  reviewedBy: uuid('reviewed_by').references(() => users.id), // Who approved/denied
  reviewedAt: timestamp('reviewed_at'),
  completedAt: timestamp('completed_at'),
  
  // Response details
  responseMessage: text('response_message'),
  dataProvided: jsonb('data_provided'), // Summary of data provided (not the actual data)
  deliveryMethod: varchar('delivery_method', { length: 50 }), // email, portal, physical
  
  // Compliance tracking
  ferpaNotificationSent: boolean('ferpa_notification_sent').default(false),
  parentalConsentRequired: boolean('parental_consent_required').default(false),
  parentalConsentReceived: boolean('parental_consent_received').default(false),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  dueDate: timestamp('due_date'), // Legal deadline for response
}, (table) => ({
  requesterStatusIdx: index('data_access_requests_requester_status_idx').on(table.requesterId, table.status),
  studentStatusIdx: index('data_access_requests_student_status_idx').on(table.studentId, table.status),
  statusDueDateIdx: index('data_access_requests_status_due_date_idx').on(table.status, table.dueDate),
}));

// Consent management for data processing
export const consentRecords = pgTable('consent_records', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Consent subject
  userId: uuid('user_id').references(() => users.id).notNull(), // Who gave consent
  studentId: uuid('student_id').references(() => users.id), // Student the consent is about (if different)
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Consent details
  consentType: varchar('consent_type', { length: 50 }).notNull(), // data_processing, directory_info, communication
  purpose: text('purpose').notNull(), // What the data will be used for
  dataTypes: text('data_types').notNull(), // JSON array of data types covered
  
  // Consent status
  status: varchar('status', { length: 20 }).notNull().default('active'), // active, withdrawn, expired
  consentGiven: boolean('consent_given').notNull(),
  consentDate: timestamp('consent_date').notNull(),
  
  // Withdrawal information
  withdrawnAt: timestamp('withdrawn_at'),
  withdrawnBy: uuid('withdrawn_by').references(() => users.id),
  withdrawalReason: text('withdrawal_reason'),
  
  // Consent mechanism
  consentMethod: varchar('consent_method', { length: 50 }).notNull(), // online_form, paper_form, verbal, implied
  ipAddress: varchar('ip_address', { length: 45 }), // For online consent
  documentUrl: text('document_url'), // Link to signed consent form
  
  // Expiration and renewal
  expiresAt: timestamp('expires_at'),
  reminderSentAt: timestamp('reminder_sent_at'),
  renewalRequired: boolean('renewal_required').default(false),
  
  // Legal compliance
  lawfulBasis: varchar('lawful_basis', { length: 100 }), // FERPA, COPPA, etc.
  minorConsentHandling: varchar('minor_consent_handling', { length: 50 }), // parental_consent, age_verification
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  userConsentIdx: index('consent_records_user_consent_idx').on(table.userId, table.consentType, table.status),
  studentConsentIdx: index('consent_records_student_consent_idx').on(table.studentId, table.consentType, table.status),
  expirationIdx: index('consent_records_expiration_idx').on(table.expiresAt, table.status),
}));

// Data retention and deletion schedule (FERPA compliance)
export const dataRetentionSchedule = pgTable('data_retention_schedule', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Data identification
  entityType: varchar('entity_type', { length: 50 }).notNull(), // user, student_profile, quiz_result, etc.
  entityId: uuid('entity_id').notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Retention details
  dataClassification: varchar('data_classification', { length: 50 }).notNull(), // educational_record, directory_info, etc.
  retentionPeriod: varchar('retention_period', { length: 50 }).notNull(), // 7_years, 3_years, indefinite
  legalRequirement: varchar('legal_requirement', { length: 100 }), // FERPA, state_law, etc.
  
  // Scheduling
  createdDate: timestamp('created_date').notNull(), // When the data was created
  retentionStartDate: timestamp('retention_start_date').notNull(), // When retention period starts
  scheduledDeletionDate: timestamp('scheduled_deletion_date').notNull(), // When data should be deleted
  
  // Status tracking
  status: varchar('status', { length: 20 }).default('active'), // active, hold, deleted, archived
  holdReason: text('hold_reason'), // Legal hold or other reason to delay deletion
  holdBy: uuid('hold_by').references(() => users.id),
  holdUntil: timestamp('hold_until'),
  
  // Deletion execution
  deletedAt: timestamp('deleted_at'),
  deletedBy: uuid('deleted_by').references(() => users.id),
  deletionMethod: varchar('deletion_method', { length: 50 }), // soft_delete, hard_delete, anonymize
  deletionConfirmed: boolean('deletion_confirmed').default(false),
  
  // Archive information
  archivedAt: timestamp('archived_at'),
  archiveLocation: text('archive_location'),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  entityIdx: index('data_retention_schedule_entity_idx').on(table.entityType, table.entityId),
  deletionDateIdx: index('data_retention_schedule_deletion_date_idx').on(table.scheduledDeletionDate, table.status),
  orgStatusIdx: index('data_retention_schedule_org_status_idx').on(table.organizationId, table.status),
}));

// Security incidents and breach notifications
export const securityIncidents = pgTable('security_incidents', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Incident identification
  incidentId: varchar('incident_id', { length: 50 }).unique().notNull(), // Human-readable incident ID
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  
  // Classification
  severity: varchar('severity', { length: 20 }).notNull(), // low, medium, high, critical
  incidentType: varchar('incident_type', { length: 50 }).notNull(), // data_breach, unauthorized_access, etc.
  
  // Affected entities
  organizationId: uuid('organization_id').references(() => organizations.id),
  affectedUsers: text('affected_users'), // JSON array of user IDs
  affectedDataTypes: text('affected_data_types'), // JSON array of data types
  
  // Timeline
  detectedAt: timestamp('detected_at').notNull(),
  occurredAt: timestamp('occurred_at'), // When the incident actually happened
  reportedAt: timestamp('reported_at'),
  resolvedAt: timestamp('resolved_at'),
  
  // Response information
  reportedBy: uuid('reported_by').references(() => users.id),
  assignedTo: uuid('assigned_to').references(() => users.id),
  responseActions: text('response_actions'), // JSON array of actions taken
  
  // Impact assessment
  dataCompromised: boolean('data_compromised').default(false),
  studentDataInvolved: boolean('student_data_involved').default(false),
  estimatedRecordsAffected: integer('estimated_records_affected'),
  
  // Legal notifications
  regulatoryNotificationRequired: boolean('regulatory_notification_required').default(false),
  regulatoryNotificationSent: boolean('regulatory_notification_sent').default(false),
  parentNotificationRequired: boolean('parent_notification_required').default(false),
  parentNotificationSent: boolean('parent_notification_sent').default(false),
  
  // Status
  status: varchar('status', { length: 20 }).default('open'), // open, investigating, resolved, closed
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  severityStatusIdx: index('security_incidents_severity_status_idx').on(table.severity, table.status),
  orgIncidentIdx: index('security_incidents_org_incident_idx').on(table.organizationId, table.detectedAt),
  studentDataIdx: index('security_incidents_student_data_idx').on(table.studentDataInvolved, table.detectedAt),
}));