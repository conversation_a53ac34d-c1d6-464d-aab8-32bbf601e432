export interface Question {
  question: string;
  options: string[];
  correctAnswer: string;
}

export interface Quiz {
  questions: Question[];
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface Material {
  id: string;
  name: string;
  content: string;
}

export interface QuizResult {
  materialId: string;
  materialName: string;
  score: number;
  total: number;
  date: string;
}

export interface FlipCard {
  term: string;
  definition: string;
}

export interface FlipCardSet {
  materialId: string;
  materialName: string;
  cards: FlipCard[];
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  requirement: string;
}

export interface Achievement {
  badgeId: string;
  unlockedAt: string;
  score?: number;
  total?: number;
}

export interface UserProgress {
  totalPoints: number;
  totalQuizzes: number;
  averageScore: number;
  achievements: Achievement[];
  streakDays: number;
  lastActiveDate: string;
}
