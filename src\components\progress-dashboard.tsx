'use client';

import { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { QuizResult, Achievement, UserProgress } from '@/lib/types';
import { calculateUserProgress, getBadgeById, BADGES } from '@/lib/gamification';
import { Trophy, Star, Target, Calendar, TrendingUp, Award, BookOpen, Brain } from 'lucide-react';

interface ProgressDashboardProps {
  quizResults: QuizResult[];
  achievements: Achievement[];
}

export default function ProgressDashboard({ quizResults, achievements }: ProgressDashboardProps) {
  const userProgress = useMemo(() => 
    calculateUserProgress(quizResults, achievements), 
    [quizResults, achievements]
  );

  const recentQuizzes = useMemo(() => 
    quizResults.slice(-5).reverse(), 
    [quizResults]
  );

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 0.9) return 'bg-green-500';
    if (percentage >= 0.8) return 'bg-blue-500';
    if (percentage >= 0.7) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getPerformanceText = (percentage: number) => {
    if (percentage >= 0.9) return 'Excellent';
    if (percentage >= 0.8) return 'Good';
    if (percentage >= 0.7) return 'Fair';
    return 'Needs Practice';
  };

  if (quizResults.length === 0) {
    return (
      <div className="grid gap-6">
        <Card>
          <CardHeader className="text-center">
            <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Your Learning Journey Starts Here!</CardTitle>
            <CardDescription>
              Take your first quiz to start tracking your progress and earning badges.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid gap-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover-glow bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Star className="h-8 w-8 text-yellow-500 hover-bounce" />
              <div>
                <p className="text-2xl font-bold text-yellow-700">{userProgress.totalPoints}</p>
                <p className="text-sm text-yellow-600">Total Points</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover-glow bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Trophy className="h-8 w-8 text-purple-500 hover-bounce" />
              <div>
                <p className="text-2xl font-bold text-purple-700">{achievements.length}</p>
                <p className="text-sm text-purple-600">Badges Earned</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover-glow bg-gradient-to-br from-blue-50 to-cyan-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Target className="h-8 w-8 text-blue-500 hover-bounce" />
              <div>
                <p className="text-2xl font-bold text-blue-700">{Math.round(userProgress.averageScore * 100)}%</p>
                <p className="text-sm text-blue-600">Average Score</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover-glow bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Brain className="h-8 w-8 text-green-500 hover-bounce" />
              <div>
                <p className="text-2xl font-bold text-green-700">{userProgress.totalQuizzes}</p>
                <p className="text-sm text-green-600">Quizzes Taken</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Achievement Showcase */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Your Badges
            </CardTitle>
            <CardDescription>
              {achievements.length > 0 
                ? `You've earned ${achievements.length} out of ${BADGES.length} badges!`
                : 'Start taking quizzes to earn your first badge!'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {achievements.length > 0 ? (
              <ScrollArea className="h-[280px]">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  {achievements.map((achievement) => {
                    const badge = getBadgeById(achievement.badgeId);
                    if (!badge) return null;
                    
                    return (
                      <div
                        key={achievement.badgeId}
                        className="flex items-center gap-3 p-3 rounded-lg border bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200"
                      >
                        <span className="text-2xl">{badge.icon}</span>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-sm truncate">{badge.name}</h3>
                          <p className="text-xs text-muted-foreground">{badge.description}</p>
                          <p className="text-xs text-blue-600">
                            Earned {new Date(achievement.unlockedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>
            ) : (
              <div className="flex flex-col items-center justify-center h-[280px] text-center">
                <Award className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">No badges yet!</p>
                <p className="text-sm text-muted-foreground">Complete quizzes to start earning badges.</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Recent Performance
            </CardTitle>
            <CardDescription>Your last 5 quiz results</CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[280px]">
              <div className="space-y-3">
                {recentQuizzes.map((result, index) => {
                  const percentage = result.score / result.total;
                  const performanceColor = getPerformanceColor(percentage);
                  const performanceText = getPerformanceText(percentage);
                  
                  return (
                    <div key={`${result.materialId}-${result.date}`} className="flex items-center justify-between p-3 rounded-lg border">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate">{result.materialName}</h4>
                        <p className="text-xs text-muted-foreground">
                          {new Date(result.date).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="text-right">
                          <p className="font-semibold text-sm">
                            {result.score}/{result.total}
                          </p>
                          <Badge variant="secondary" className={`text-xs ${performanceColor} text-white`}>
                            {performanceText}
                          </Badge>
                        </div>
                        <div className="w-12">
                          <Progress value={percentage * 100} className="h-2" />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Progress Goals */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Next Goals
          </CardTitle>
          <CardDescription>Keep learning to unlock these badges!</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {BADGES.filter(badge => !achievements.some(a => a.badgeId === badge.id))
              .slice(0, 6)
              .map((badge) => (
                <div
                  key={badge.id}
                  className="flex items-center gap-3 p-3 rounded-lg border border-dashed bg-muted/20"
                >
                  <span className="text-2xl opacity-50">{badge.icon}</span>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-sm truncate">{badge.name}</h3>
                    <p className="text-xs text-muted-foreground">{badge.requirement}</p>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}