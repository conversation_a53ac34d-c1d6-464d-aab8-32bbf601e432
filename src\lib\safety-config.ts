import {z} from 'zod';

// Safety configuration types
export interface SafetySettings {
	contentFiltering: {
		enabled: boolean;
		strictMode: boolean;
		ageGroup: 'elementary' | 'middle' | 'high' | 'adult';
		customBlockedWords: string[];
		allowEducationalExceptions: boolean;
	};
	aiResponseValidation: {
		enabled: boolean;
		requireEducationalValue: boolean;
		maxResponseLength: number;
		educationalValueThreshold: number;
		validateJsonStructure: boolean;
	};
	complianceSettings: {
		enforceAgeVerification: boolean;
		requireParentalConsent: boolean;
		enableAuditLogging: boolean;
		dataRetentionDays: number;
		anonymizeUserData: boolean;
	};
	apiSafety: {
		enableRateLimiting: boolean;
		requestsPerMinute: number;
		enableInputSanitization: boolean;
		enableOutputFiltering: boolean;
		logSafetyViolations: boolean;
	};
	uiSafety: {
		showSafetyIndicators: boolean;
		enableContentPreview: boolean;
		requireConfirmationForRiskyContent: boolean;
		showEducationalGuidelines: boolean;
	};
}

// Schema for validation
const SafetySettingsSchema = z.object({
	contentFiltering: z.object({
		enabled: z.boolean().default(true),
		strictMode: z.boolean().default(true),
		ageGroup: z
			.enum(['elementary', 'middle', 'high', 'adult'])
			.default('middle'),
		customBlockedWords: z.array(z.string()).default([]),
		allowEducationalExceptions: z.boolean().default(true),
	}),
	aiResponseValidation: z.object({
		enabled: z.boolean().default(true),
		requireEducationalValue: z.boolean().default(true),
		maxResponseLength: z.number().min(100).max(5000).default(2000),
		educationalValueThreshold: z.number().min(0).max(1).default(0.6),
		validateJsonStructure: z.boolean().default(true),
	}),
	complianceSettings: z.object({
		enforceAgeVerification: z.boolean().default(true),
		requireParentalConsent: z.boolean().default(true),
		enableAuditLogging: z.boolean().default(true),
		dataRetentionDays: z.number().min(30).max(2555).default(365),
		anonymizeUserData: z.boolean().default(true),
	}),
	apiSafety: z.object({
		enableRateLimiting: z.boolean().default(true),
		requestsPerMinute: z.number().min(1).max(1000).default(30),
		enableInputSanitization: z.boolean().default(true),
		enableOutputFiltering: z.boolean().default(true),
		logSafetyViolations: z.boolean().default(true),
	}),
	uiSafety: z.object({
		showSafetyIndicators: z.boolean().default(true),
		enableContentPreview: z.boolean().default(true),
		requireConfirmationForRiskyContent: z.boolean().default(true),
		showEducationalGuidelines: z.boolean().default(true),
	}),
});

// Default safety settings
export const DEFAULT_SAFETY_SETTINGS: SafetySettings = {
	contentFiltering: {
		enabled: true,
		strictMode: true,
		ageGroup: 'middle',
		customBlockedWords: [],
		allowEducationalExceptions: true,
	},
	aiResponseValidation: {
		enabled: true,
		requireEducationalValue: true,
		maxResponseLength: 2000,
		educationalValueThreshold: 0.6,
		validateJsonStructure: true,
	},
	complianceSettings: {
		enforceAgeVerification: true,
		requireParentalConsent: true,
		enableAuditLogging: true,
		dataRetentionDays: 365,
		anonymizeUserData: true,
	},
	apiSafety: {
		enableRateLimiting: true,
		requestsPerMinute: 30,
		enableInputSanitization: true,
		enableOutputFiltering: true,
		logSafetyViolations: true,
	},
	uiSafety: {
		showSafetyIndicators: true,
		enableContentPreview: true,
		requireConfirmationForRiskyContent: true,
		showEducationalGuidelines: true,
	},
};

// Age-group specific presets
export const AGE_GROUP_PRESETS: Record<string, Partial<SafetySettings>> = {
	elementary: {
		contentFiltering: {
			enabled: true,
			strictMode: true,
			ageGroup: 'elementary',
			customBlockedWords: ['politics', 'religion', 'war', 'death', 'scary'],
			allowEducationalExceptions: false,
		},
		aiResponseValidation: {
			enabled: true,
			requireEducationalValue: true,
			maxResponseLength: 500,
			educationalValueThreshold: 0.8,
			validateJsonStructure: true,
		},
		complianceSettings: {
			enforceAgeVerification: true,
			requireParentalConsent: true,
			enableAuditLogging: true,
			dataRetentionDays: 180, // Shorter retention for younger children
			anonymizeUserData: true,
		},
	},
	middle: {
		contentFiltering: {
			enabled: true,
			strictMode: true,
			ageGroup: 'middle',
			customBlockedWords: ['inappropriate', 'explicit'],
			allowEducationalExceptions: true,
		},
		aiResponseValidation: {
			enabled: true,
			requireEducationalValue: true,
			maxResponseLength: 1500,
			educationalValueThreshold: 0.6,
			validateJsonStructure: true,
		},
	},
	high: {
		contentFiltering: {
			enabled: true,
			strictMode: false,
			ageGroup: 'high',
			customBlockedWords: [],
			allowEducationalExceptions: true,
		},
		aiResponseValidation: {
			enabled: true,
			requireEducationalValue: true,
			maxResponseLength: 2500,
			educationalValueThreshold: 0.5,
			validateJsonStructure: true,
		},
		complianceSettings: {
			enforceAgeVerification: false, // High school students typically 14+
			requireParentalConsent: false,
			enableAuditLogging: true,
			dataRetentionDays: 730, // 2 years for academic records
			anonymizeUserData: true,
		},
	},
	adult: {
		contentFiltering: {
			enabled: true,
			strictMode: false,
			ageGroup: 'adult',
			customBlockedWords: [],
			allowEducationalExceptions: true,
		},
		aiResponseValidation: {
			enabled: true,
			requireEducationalValue: false,
			maxResponseLength: 3000,
			educationalValueThreshold: 0.3,
			validateJsonStructure: true,
		},
		complianceSettings: {
			enforceAgeVerification: false,
			requireParentalConsent: false,
			enableAuditLogging: true,
			dataRetentionDays: 1095, // 3 years
			anonymizeUserData: false,
		},
	},
};

// Environment-specific settings
export const ENVIRONMENT_PRESETS: Record<string, Partial<SafetySettings>> = {
	development: {
		contentFiltering: {
			enabled: false,
			strictMode: false,
			ageGroup: 'middle',
			customBlockedWords: [],
			allowEducationalExceptions: true,
		},
		apiSafety: {
			enableRateLimiting: false,
			requestsPerMinute: 1000,
			enableInputSanitization: false,
			enableOutputFiltering: false,
			logSafetyViolations: true,
		},
		complianceSettings: {
			enforceAgeVerification: false,
			requireParentalConsent: false,
			enableAuditLogging: false,
			dataRetentionDays: 365,
			anonymizeUserData: false,
		},
	},
	staging: {
		contentFiltering: {
			enabled: true,
			strictMode: true,
			ageGroup: 'middle',
			customBlockedWords: [],
			allowEducationalExceptions: true,
		},
		apiSafety: {
			enableRateLimiting: true,
			requestsPerMinute: 100,
			enableInputSanitization: true,
			enableOutputFiltering: true,
			logSafetyViolations: true,
		},
		complianceSettings: {
			enforceAgeVerification: true,
			requireParentalConsent: true,
			enableAuditLogging: true,
			dataRetentionDays: 1095,
			anonymizeUserData: true,
		},
	},
	production: {
		contentFiltering: {
			enabled: true,
			strictMode: true,
			ageGroup: 'middle',
			customBlockedWords: [],
			allowEducationalExceptions: false,
		},
		aiResponseValidation: {
			enabled: true,
			requireEducationalValue: true,
			maxResponseLength: 5000,
			educationalValueThreshold: 0.7,
			validateJsonStructure: true,
		},
		complianceSettings: {
			enforceAgeVerification: true,
			requireParentalConsent: true,
			enableAuditLogging: true,
			dataRetentionDays: 1095,
			anonymizeUserData: true,
		},
		apiSafety: {
			enableRateLimiting: true,
			requestsPerMinute: 30,
			enableInputSanitization: true,
			enableOutputFiltering: true,
			logSafetyViolations: true,
		},
	},
};

export class SafetyConfigManager {
	private settings: SafetySettings;
	private configVersion: string = '1.0.0';

	constructor(initialSettings?: Partial<SafetySettings>) {
		this.settings = this.mergeSettings(
			DEFAULT_SAFETY_SETTINGS,
			initialSettings || {}
		);
	}

	/**
	 * Get current safety settings
	 */
	getSettings(): SafetySettings {
		return {...this.settings};
	}

	/**
	 * Update safety settings with validation
	 */
	updateSettings(updates: Partial<SafetySettings>): SafetySettings {
		try {
			const newSettings = this.mergeSettings(this.settings, updates);
			const validatedSettings = SafetySettingsSchema.parse(newSettings);
			this.settings = validatedSettings;
			return this.settings;
		} catch (error) {
			console.error('Invalid safety settings update:', error);
			throw new Error(
				'Failed to update safety settings: Invalid configuration'
			);
		}
	}

	/**
	 * Apply age-group specific preset
	 */
	applyAgeGroupPreset(
		ageGroup: keyof typeof AGE_GROUP_PRESETS
	): SafetySettings {
		const preset = AGE_GROUP_PRESETS[ageGroup];
		if (!preset) {
			throw new Error(`Unknown age group preset: ${ageGroup}`);
		}

		return this.updateSettings(preset);
	}

	/**
	 * Apply environment-specific preset
	 */
	applyEnvironmentPreset(
		environment: keyof typeof ENVIRONMENT_PRESETS
	): SafetySettings {
		const preset = ENVIRONMENT_PRESETS[environment];
		if (!preset) {
			throw new Error(`Unknown environment preset: ${environment}`);
		}

		return this.updateSettings(preset);
	}

	/**
	 * Get safety level summary
	 */
	getSafetyLevel(): {
		level: 'strict' | 'moderate' | 'relaxed';
		description: string;
		activeFeatures: string[];
	} {
		const {contentFiltering, aiResponseValidation, complianceSettings} =
			this.settings;

		const activeFeatures: string[] = [];
		let strictnessScore = 0;

		if (contentFiltering.enabled) {
			activeFeatures.push('Content Filtering');
			strictnessScore += contentFiltering.strictMode ? 3 : 1;
		}

		if (aiResponseValidation.enabled) {
			activeFeatures.push('AI Response Validation');
			strictnessScore += aiResponseValidation.requireEducationalValue ? 2 : 1;
		}

		if (complianceSettings.enforceAgeVerification) {
			activeFeatures.push('Age Verification');
			strictnessScore += 2;
		}

		if (complianceSettings.requireParentalConsent) {
			activeFeatures.push('Parental Consent');
			strictnessScore += 2;
		}

		if (complianceSettings.enableAuditLogging) {
			activeFeatures.push('Audit Logging');
			strictnessScore += 1;
		}

		let level: 'strict' | 'moderate' | 'relaxed';
		let description: string;

		if (strictnessScore >= 8) {
			level = 'strict';
			description =
				'Maximum safety protections enabled for educational environment';
		} else if (strictnessScore >= 4) {
			level = 'moderate';
			description = 'Balanced safety protections with educational flexibility';
		} else {
			level = 'relaxed';
			description = 'Minimal safety restrictions for advanced users';
		}

		return {level, description, activeFeatures};
	}

	/**
	 * Validate current configuration
	 */
	validateConfiguration(): {
		isValid: boolean;
		warnings: string[];
		errors: string[];
	} {
		const warnings: string[] = [];
		const errors: string[] = [];

		// Check for potential security issues
		if (!this.settings.contentFiltering.enabled) {
			warnings.push(
				'Content filtering is disabled - may allow inappropriate content'
			);
		}

		if (!this.settings.aiResponseValidation.enabled) {
			warnings.push(
				'AI response validation is disabled - may allow inappropriate AI responses'
			);
		}

		if (!this.settings.apiSafety.enableRateLimiting) {
			warnings.push(
				'API rate limiting is disabled - system may be vulnerable to abuse'
			);
		}

		// Check for compliance issues
		if (
			this.settings.contentFiltering.ageGroup === 'elementary' &&
			!this.settings.complianceSettings.requireParentalConsent
		) {
			errors.push('Parental consent is required for elementary age group');
		}

		if (this.settings.complianceSettings.dataRetentionDays > 2555) {
			warnings.push(
				'Data retention period exceeds 7 years - may violate FERPA guidelines'
			);
		}

		// Check for performance issues
		if (this.settings.aiResponseValidation.maxResponseLength > 5000) {
			warnings.push(
				'Maximum response length is very high - may impact performance'
			);
		}

		return {
			isValid: errors.length === 0,
			warnings,
			errors,
		};
	}

	/**
	 * Export settings for backup or transfer
	 */
	exportSettings(): string {
		return JSON.stringify(
			{
				settings: this.settings,
				version: this.configVersion,
				exportDate: new Date().toISOString(),
			},
			null,
			2
		);
	}

	/**
	 * Import settings from backup
	 */
	importSettings(settingsJson: string): SafetySettings {
		try {
			const imported = JSON.parse(settingsJson);

			if (imported.version !== this.configVersion) {
				console.warn(
					`Importing settings from different version: ${imported.version} → ${this.configVersion}`
				);
			}

			return this.updateSettings(imported.settings);
		} catch (error) {
			throw new Error('Failed to import settings: Invalid JSON or format');
		}
	}

	/**
	 * Reset to default settings
	 */
	resetToDefaults(): SafetySettings {
		this.settings = {...DEFAULT_SAFETY_SETTINGS};
		return this.settings;
	}

	// Private helper methods
	private mergeSettings(
		base: SafetySettings,
		updates: Partial<SafetySettings>
	): SafetySettings {
		return {
			contentFiltering: {...base.contentFiltering, ...updates.contentFiltering},
			aiResponseValidation: {
				...base.aiResponseValidation,
				...updates.aiResponseValidation,
			},
			complianceSettings: {
				...base.complianceSettings,
				...updates.complianceSettings,
			},
			apiSafety: {...base.apiSafety, ...updates.apiSafety},
			uiSafety: {...base.uiSafety, ...updates.uiSafety},
		};
	}
}

// Utility functions
export function createSafetyConfig(options?: {
	ageGroup?: keyof typeof AGE_GROUP_PRESETS;
	environment?: keyof typeof ENVIRONMENT_PRESETS;
	customSettings?: Partial<SafetySettings>;
}): SafetyConfigManager {
	const manager = new SafetyConfigManager();

	if (options?.ageGroup) {
		manager.applyAgeGroupPreset(options.ageGroup);
	}

	if (options?.environment) {
		manager.applyEnvironmentPreset(options.environment);
	}

	if (options?.customSettings) {
		manager.updateSettings(options.customSettings);
	}

	return manager;
}

export function getSafetyPreset(
	ageGroup: keyof typeof AGE_GROUP_PRESETS,
	environment: keyof typeof ENVIRONMENT_PRESETS = 'production'
): SafetySettings {
	const manager = createSafetyConfig({ageGroup, environment});
	return manager.getSettings();
}

// Export singleton for global use
export const globalSafetyConfig = createSafetyConfig({
	ageGroup: 'middle',
	environment:
		process.env.NODE_ENV === 'production' ? 'production' : 'development',
});

// Export types are already defined as interfaces above
