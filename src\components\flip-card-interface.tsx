'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { FlipCardSet, FlipCard } from '@/lib/types';
import { ArrowLeft, ArrowRight, Layers } from 'lucide-react';

interface FlipCardInterfaceProps {
  flipCardSet: FlipCardSet | null;
}

export default function FlipCardInterface({ flipCardSet }: FlipCardInterfaceProps) {
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);

  const handleNext = () => {
    if (flipCardSet && currentCardIndex < flipCardSet.cards.length - 1) {
      setCurrentCardIndex(currentCardIndex + 1);
      setIsFlipped(false);
    }
  };

  const handlePrev = () => {
    if (currentCardIndex > 0) {
      setCurrentCardIndex(currentCardIndex - 1);
      setIsFlipped(false);
    }
  };

  if (!flipCardSet) {
    return (
      <div className="flex h-full flex-col items-center justify-center rounded-lg border border-dashed bg-card/50 p-8 text-center">
        <h3 className="font-headline text-xl font-semibold">No Flip Cards Generated</h3>
        <p className="text-muted-foreground">
          Please select a material and click &quot;Generate Flip Cards&quot; from the 'My Materials' tab.
        </p>
      </div>
    );
  }

  const card: FlipCard = flipCardSet.cards[currentCardIndex];

  return (
    <div className="flex flex-col items-center gap-6">
      <h2 className="font-headline text-2xl font-bold">
        Flip Cards for &quot;{flipCardSet.materialName}&quot;
      </h2>
      
      <div className={`flip-card h-80 w-full max-w-2xl ${isFlipped ? 'flipped' : ''}`} onClick={() => setIsFlipped(!isFlipped)}>
        <div className="flip-card-inner">
          <div className="flip-card-front bg-card border-2 border-primary shadow-lg">
            <p className="text-2xl font-semibold text-primary">{card.term}</p>
          </div>
          <div className="flip-card-back bg-primary border-2 border-primary shadow-lg">
            <p className="text-lg text-primary-foreground">{card.definition}</p>
          </div>
        </div>
      </div>

      <div className="flex w-full max-w-2xl items-center justify-between">
        <Button onClick={handlePrev} disabled={currentCardIndex === 0} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" /> Previous
        </Button>
        <p className="text-sm text-muted-foreground">
          Card {currentCardIndex + 1} of {flipCardSet.cards.length}
        </p>
        <Button onClick={handleNext} disabled={currentCardIndex === flipCardSet.cards.length - 1} variant="outline">
          Next <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
