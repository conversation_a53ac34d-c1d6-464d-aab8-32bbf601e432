'use server';

/**
 * @fileOverview Generates flip cards from uploaded materials.
 *
 * - generateFlipCards - A function that generates flip cards from provided learning materials.
 * - GenerateFlipCardsInput - The input type for the generateFlipCards function.
 * - GenerateFlipCardsOutput - The return type for the generateFlipCards function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateFlipCardsInputSchema = z.object({
  material: z.string().describe('The learning material to generate flip cards from.'),
  numberOfCards: z.number().min(1).max(20).default(10).describe('The number of flip cards to generate.'),
});
export type GenerateFlipCardsInput = z.infer<typeof GenerateFlipCardsInputSchema>;

const GenerateFlipCardsOutputSchema = z.object({
  cards: z.string().describe('The generated flip cards in JSON format.'),
});
export type GenerateFlipCardsOutput = z.infer<typeof GenerateFlipCardsOutputSchema>;

export async function generateFlipCards(input: GenerateFlipCardsInput): Promise<GenerateFlipCardsOutput> {
  try {
    const result = await generateFlipCardsFlow(input);
    
    // Validate flip cards format
    try {
      const parsed = JSON.parse(result.cards);
      if (!parsed.cards || !Array.isArray(parsed.cards)) {
        throw new Error('Invalid flip cards structure: missing cards array');
      }
    } catch (parseError) {
      throw new Error('Generated flip cards are not valid JSON');
    }
    
    return result;
  } catch (error) {
    console.error('Flip cards generation error:', error);
    throw new Error(`Failed to generate flip cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

const generateFlipCardsPrompt = ai.definePrompt({
  name: 'generateFlipCardsPrompt',
  input: {schema: GenerateFlipCardsInputSchema},
  output: {schema: GenerateFlipCardsOutputSchema},
  prompt: `You are a flashcard generator. You will receive learning material and generate a set of flip cards based on it.

  The flip cards should be in JSON format and have the following structure:
  {
    "cards": [
      {
        "term": "The key term or concept",
        "definition": "The definition or explanation of the term"
      }
    ]
  }

  Generate {{numberOfCards}} cards.

  Material: {{{material}}}
  `,
});

const generateFlipCardsFlow = ai.defineFlow(
  {
    name: 'generateFlipCardsFlow',
    inputSchema: GenerateFlipCardsInputSchema,
    outputSchema: GenerateFlipCardsOutputSchema,
  },
  async input => {
    try {
      const {output} = await generateFlipCardsPrompt(input);
      
      if (!output) {
        throw new Error('No output received from AI model');
      }
      
      return output;
    } catch (error) {
      console.error('Flip cards generation flow error:', error);
      throw error;
    }
  }
);
