'use server';

/**
 * @fileOverview Generates flip cards from uploaded materials.
 *
 * - generateFlipCards - A function that generates flip cards from provided learning materials.
 * - GenerateFlipCardsInput - The input type for the generateFlipCards function.
 * - GenerateFlipCardsOutput - The return type for the generateFlipCards function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateFlipCardsInputSchema = z.object({
  material: z.string().describe('The learning material to generate flip cards from.'),
  numberOfCards: z.number().min(1).max(20).default(10).describe('The number of flip cards to generate.'),
});
export type GenerateFlipCardsInput = z.infer<typeof GenerateFlipCardsInputSchema>;

const FlipCardSchema = z.object({
  term: z.string().describe('The key term or concept'),
  definition: z.string().describe('The definition or explanation of the term'),
});

const GenerateFlipCardsOutputSchema = z.object({
  cards: z.array(FlipCardSchema).describe('Array of generated flip cards'),
});
export type GenerateFlipCardsOutput = z.infer<typeof GenerateFlipCardsOutputSchema>;

export async function generateFlipCards(input: GenerateFlipCardsInput): Promise<GenerateFlipCardsOutput> {
  try {
    const result = await generateFlipCardsFlow(input);
    
    // Validate the result structure using Zod schema
    const validatedResult = GenerateFlipCardsOutputSchema.parse(result);
    
    // Additional validation for flip cards content
    if (!validatedResult.cards || validatedResult.cards.length === 0) {
      throw new Error('No flip cards were generated');
    }
    
    for (const card of validatedResult.cards) {
      if (!card.term || !card.definition || card.term.trim() === '' || card.definition.trim() === '') {
        throw new Error('Invalid flip card: term and definition are required');
      }
    }
    
    return validatedResult;
  } catch (error) {
    console.error('Flip cards generation error:', error);
    
    if (error instanceof z.ZodError) {
      console.error('Validation error details:', error.errors);
      throw new Error(`Invalid flip cards format: ${error.errors.map(e => e.message).join(', ')}`);
    }
    
    throw new Error(`Failed to generate flip cards: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

const generateFlipCardsPrompt = ai.definePrompt({
  name: 'generateFlipCardsPrompt',
  input: {schema: GenerateFlipCardsInputSchema},
  output: {schema: GenerateFlipCardsOutputSchema},
  prompt: `You are a flashcard generator. You will receive learning material and generate a set of flip cards based on it.

Create {{numberOfCards}} flip cards from the material provided. Each card should have a clear term and its corresponding definition or explanation.

Important guidelines:
- Focus on key concepts, terms, definitions, and important facts
- Make terms concise but specific
- Provide clear, accurate definitions
- Ensure each card tests important knowledge from the material
- Avoid duplicates or overly similar cards

Material: {{{material}}}

Generate exactly {{numberOfCards}} flip cards.`,
});

const generateFlipCardsFlow = ai.defineFlow(
  {
    name: 'generateFlipCardsFlow',
    inputSchema: GenerateFlipCardsInputSchema,
    outputSchema: GenerateFlipCardsOutputSchema,
  },
  async input => {
    try {
      const {output} = await generateFlipCardsPrompt(input);
      
      if (!output) {
        throw new Error('No output received from AI model');
      }
      
      return output;
    } catch (error) {
      console.error('Flip cards generation flow error:', error);
      throw error;
    }
  }
);
