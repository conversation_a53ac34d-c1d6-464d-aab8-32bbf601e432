// Simple safety system test
const { ContentSafetyFilter } = require('./src/lib/content-safety.ts');

async function testContentSafety() {
  console.log('🧪 Testing Content Safety System...\n');
  
  const filter = new ContentSafetyFilter();
  filter.updateConfig({
    strictMode: true,
    ageGroup: 'middle',
    educationalContext: true,
  });

  // Test 1: Appropriate educational content
  console.log('Test 1: Educational Content');
  const educationalContent = "Photosynthesis is the process by which plants use sunlight to create energy from carbon dioxide and water.";
  try {
    const result1 = await filter.checkContent(educationalContent);
    console.log(`✅ Safe: ${result1.isAppropriate} (confidence: ${result1.confidence})`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  // Test 2: Inappropriate content
  console.log('\nTest 2: Inappropriate Content');
  const inappropriateContent = "This content contains inappropriate language and violent themes not suitable for students.";
  try {
    const result2 = await filter.checkContent(inappropriateContent);
    console.log(`🛡️ Blocked: ${!result2.isAppropriate} (violations: ${result2.violations.length})`);
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }

  console.log('\n✅ Safety system integration test completed!');
}

testContentSafety().catch(console.error);