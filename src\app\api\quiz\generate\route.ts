import { NextRequest, NextResponse } from 'next/server';
import { generateQuiz, GenerateQuizInput } from '@/ai/flows/generate-quiz';
import { z } from 'zod';

const RequestSchema = z.object({
  material: z.string().min(1, 'Material content is required'),
  numberOfQuestions: z.number().int().min(1).max(20).default(5),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedInput = RequestSchema.parse(body);
    
    // Generate quiz using AI flow
    const result = await generateQuiz(validatedInput);
    
    // Validate AI output is valid JSON
    try {
      JSON.parse(result.quiz);
    } catch {
      throw new Error('Invalid quiz format generated');
    }
    
    return NextResponse.json({ 
      success: true, 
      data: result 
    });
    
  } catch (error) {
    console.error('Quiz generation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid input data',
          details: error.errors 
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate quiz' 
      },
      { status: 500 }
    );
  }
}