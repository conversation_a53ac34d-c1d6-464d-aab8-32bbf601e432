import {NextRequest, NextResponse} from 'next/server';
import {generateQuiz, GenerateQuizInput} from '@/ai/flows/generate-quiz';
import {withSafety} from '@/lib/safety-middleware';
import {validateAIResponse} from '@/lib/ai-response-validator';
import {contentSafetyFilter, ContentSafetyError} from '@/lib/content-safety';
import {z} from 'zod';

const RequestSchema = z.object({
	material: z.string().min(1, 'Material content is required'),
	numberOfQuestions: z.number().int().min(1).max(20).default(5),
	ageGroup: z.enum(['elementary', 'middle', 'high', 'adult']).default('middle'),
});

async function handleQuizGeneration(request: NextRequest) {
	let validatedInput: z.infer<typeof RequestSchema> | null = null;

	try {
		const body = await request.json();

		// Validate input schema
		validatedInput = RequestSchema.parse(body);

		// Step 1: Content safety check on input material
		contentSafetyFilter.updateConfig({
			strictMode: true,
			ageGroup: validatedInput.ageGroup,
			educationalContext: true,
		});

		const inputSafetyResult = await contentSafetyFilter.checkContent(
			validatedInput.material
		);
		if (!inputSafetyResult.isAppropriate) {
			throw new ContentSafetyError(
				'Input material contains inappropriate content',
				inputSafetyResult.violations,
				inputSafetyResult.riskLevel
			);
		}

		// Step 2: Generate quiz using AI flow
		const result = await generateQuiz({
			material: validatedInput.material,
			numberOfQuestions: validatedInput.numberOfQuestions,
		});

		// Step 3: Validate AI response for safety and quality
		const outputValidation = await validateAIResponse(
			result.quiz,
			'quiz',
			validatedInput.ageGroup
		);

		if (!outputValidation.isValid) {
			console.warn('Quiz failed safety validation:', outputValidation.issues);

			// If it's not valid, try to regenerate once
			if (
				outputValidation.issues.includes('inappropriate-content') ||
				outputValidation.issues.includes('prompt-injection-detected')
			) {
				throw new ContentSafetyError(
					'Generated quiz contains inappropriate content',
					outputValidation.issues,
					'high'
				);
			}

			// For less critical issues, log but allow with warnings
			console.log(
				'Quiz has minor issues but is acceptable:',
				outputValidation.suggestions
			);
		}

		return NextResponse.json({
			success: true,
			data: result,
			safetyInfo: {
				inputSafetyScore: inputSafetyResult.confidence,
				outputSafetyScore: outputValidation.safetyScore,
				educationalValue: outputValidation.educationalValue,
				ageAppropriate: outputValidation.ageAppropriate,
				suggestions: outputValidation.suggestions,
			},
		});
	} catch (error) {
		console.error('Quiz generation error:', error);

		if (error instanceof ContentSafetyError) {
			return NextResponse.json(
				{
					success: false,
					error: error.message,
					violations: error.violations,
					riskLevel: error.riskLevel,
					safetyInfo: {
						message:
							'Content did not meet safety guidelines for educational use',
						ageGroup: validatedInput?.ageGroup || 'middle',
					},
				},
				{status: 400}
			);
		}

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Invalid input data',
					details: error.errors,
				},
				{status: 400}
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to generate quiz',
				message: error instanceof Error ? error.message : 'Unknown error',
			},
			{status: 500}
		);
	}
}

// Export the safety-wrapped handler
export const POST = withSafety(handleQuizGeneration, {
	context: 'quiz',
	inputFields: ['material'],
	outputField: 'quiz',
	config: {
		enableInputFiltering: true,
		enableOutputFiltering: true,
		ageGroup: 'middle',
		strictMode: true,
		logViolations: true,
		blockOnViolation: true,
	},
});
