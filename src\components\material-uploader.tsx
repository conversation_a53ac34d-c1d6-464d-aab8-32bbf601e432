'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { FileUp, CheckCircle, XCircle, BookOpen, Type, FileText, Lightbulb } from 'lucide-react';

interface MaterialUploaderProps {
  onUpload: (name: string, content: string) => void;
  disabled: boolean;
}

export default function MaterialUploader({ onUpload, disabled }: MaterialUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [topicInput, setTopicInput] = useState('');
  const [textContent, setTextContent] = useState('');
  const [activeTab, setActiveTab] = useState('topic');
  const { toast } = useToast();

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (disabled) return;
    setIsUploading(true);
    const file = acceptedFiles[0];
    if (file) {
      if (file.type !== 'text/plain' && file.type !== 'application/pdf') {
        toast({
          title: 'Invalid File Type',
          description: 'Please upload a .txt or .pdf file.',
          variant: 'destructive',
        });
        setIsUploading(false);
        return;
      }
      if (file.type === 'application/pdf') {
        // For PDF files, we'll create a placeholder content that indicates this is a PDF
        // In a production environment, you would use a library like pdf-parse or PDF.js
        const content = `PDF Document: ${file.name}\n\nThis is a PDF document that has been uploaded. For full PDF text extraction functionality, a PDF parsing library like pdf-parse would be integrated.\n\nFile size: ${(file.size / 1024).toFixed(2)} KB\nType: PDF Document\n\nThe AI can still help with general questions about the document based on the filename and any context you provide.`;
        
        onUpload(file.name, content);
        toast({
          title: 'PDF Uploaded',
          description: `${file.name} has been added. Note: Full PDF text extraction requires additional setup.`,
          action: <CheckCircle className="text-green-500" />,
        });
        setIsUploading(false);
      } else {
        // Handle text files as before
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          onUpload(file.name, content);
          toast({
            title: 'Upload Successful',
            description: `${file.name} has been added to your materials.`,
            action: <CheckCircle className="text-green-500" />,
          });
          setIsUploading(false);
        };
        reader.onerror = () => {
          toast({
            title: 'Upload Failed',
            description: 'There was an error reading the file.',
            variant: 'destructive',
          });
          setIsUploading(false);
        };
        reader.readAsText(file);
      }
    } else {
      setIsUploading(false);
    }
  }, [onUpload, toast, disabled]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({ 
    onDrop, 
    multiple: false, 
    disabled,
    accept: {
      'text/plain': ['.txt'],
      'application/pdf': ['.pdf']
    }
  });

  const handleTopicSubmit = () => {
    if (!topicInput.trim()) {
      toast({
        title: 'Topic Required',
        description: 'Please enter a topic to study.',
        variant: 'destructive',
      });
      return;
    }
    
    const materialName = `Topic: ${topicInput}`;
    const content = `Study Topic: ${topicInput}\n\nThis is a general topic for AI-generated study content. The AI will provide age-appropriate information and study materials based on this topic.`;
    
    onUpload(materialName, content);
    setTopicInput('');
    toast({
      title: 'Topic Added',
      description: `${topicInput} has been added to your materials.`,
      action: <CheckCircle className="text-green-500" />,
    });
  };

  const handleTextSubmit = () => {
    if (!textContent.trim()) {
      toast({
        title: 'Content Required',
        description: 'Please enter some text content.',
        variant: 'destructive',
      });
      return;
    }
    
    const materialName = `Text Content: ${textContent.substring(0, 30)}${textContent.length > 30 ? '...' : ''}`;
    
    onUpload(materialName, textContent);
    setTextContent('');
    toast({
      title: 'Content Added',
      description: 'Your text content has been added to materials.',
      action: <CheckCircle className="text-green-500" />,
    });
  };

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="topic" className="flex items-center gap-2">
          <Lightbulb className="h-4 w-4" />
          Topic
        </TabsTrigger>
        <TabsTrigger value="text" className="flex items-center gap-2">
          <Type className="h-4 w-4" />
          Text
        </TabsTrigger>
        <TabsTrigger value="file" className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          File
        </TabsTrigger>
      </TabsList>

      <TabsContent value="topic" className="mt-4">
        <div className="space-y-4">
          <div className="text-center mb-4">
            <Lightbulb className="h-12 w-12 text-primary mx-auto mb-2" />
            <h3 className="font-semibold text-lg">Study a Topic</h3>
            <p className="text-sm text-muted-foreground">Enter any topic you want to learn about</p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="topic">What would you like to study?</Label>
            <Input
              id="topic"
              placeholder="e.g., Ancient Egypt, Photosynthesis, The Solar System..."
              value={topicInput}
              onChange={(e) => setTopicInput(e.target.value)}
              disabled={disabled}
              onKeyDown={(e) => e.key === 'Enter' && handleTopicSubmit()}
            />
          </div>
          <Button 
            onClick={handleTopicSubmit} 
            disabled={disabled || !topicInput.trim()}
            className="w-full"
          >
            <BookOpen className="mr-2 h-4 w-4" />
            Start Learning
          </Button>
        </div>
      </TabsContent>

      <TabsContent value="text" className="mt-4">
        <div className="space-y-4">
          <div className="text-center mb-4">
            <Type className="h-12 w-12 text-primary mx-auto mb-2" />
            <h3 className="font-semibold text-lg">Paste Text Content</h3>
            <p className="text-sm text-muted-foreground">Copy and paste content from articles, homework, or notes</p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="textcontent">Paste your content here</Label>
            <Textarea
              id="textcontent"
              placeholder="Paste your text content here..."
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
              disabled={disabled}
              className="min-h-[150px]"
            />
          </div>
          <Button 
            onClick={handleTextSubmit} 
            disabled={disabled || !textContent.trim()}
            className="w-full"
          >
            <CheckCircle className="mr-2 h-4 w-4" />
            Add Content
          </Button>
        </div>
      </TabsContent>

      <TabsContent value="file" className="mt-4">
        <div
          {...getRootProps()}
          className={`flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-8 text-center transition-colors duration-200 ${
            isDragActive ? 'border-primary bg-primary/10' : 'border-border hover:border-primary/50'
          } ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
        >
          <input {...getInputProps()} />
          <FileUp className="mb-4 h-12 w-12 text-muted-foreground" />
          {isDragActive ? (
            <p className="font-semibold text-primary">Drop the file here ...</p>
          ) : (
            <>
              <p className="font-semibold">Drag & drop your .txt or .pdf file here, or click to select</p>
              <p className="text-sm text-muted-foreground">Upload your textbooks, notes, or course materials</p>
            </>
          )}
        </div>
      </TabsContent>
    </Tabs>
  );
}
