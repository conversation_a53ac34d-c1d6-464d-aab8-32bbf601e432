import { pgTable, uuid, varchar, timestamp, boolean, text, integer, decimal } from 'drizzle-orm/pg-core';
import { users, accountTypeEnum } from './auth';

// Organizations table for multi-tenant architecture
export const organizations = pgTable('organizations', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // Basic organization info
  name: varchar('name', { length: 255 }).notNull(),
  displayName: varchar('display_name', { length: 255 }),
  description: text('description'),
  logoUrl: text('logo_url'),
  websiteUrl: text('website_url'),
  
  // Organization type and configuration
  type: accountTypeEnum('type').notNull().default('school'),
  subdomain: varchar('subdomain', { length: 100 }).unique(), // For custom domains
  
  // Contact information
  contactEmail: varchar('contact_email', { length: 255 }),
  contactPhone: varchar('contact_phone', { length: 50 }),
  
  // Address information
  addressLine1: varchar('address_line_1', { length: 255 }),
  addressLine2: varchar('address_line_2', { length: 255 }),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 100 }),
  postalCode: varchar('postal_code', { length: 20 }),
  country: varchar('country', { length: 100 }).default('US'),
  
  // FERPA compliance settings
  ferpaOfficerName: varchar('ferpa_officer_name', { length: 255 }),
  ferpaOfficerEmail: varchar('ferpa_officer_email', { length: 255 }),
  ferpaOfficerPhone: varchar('ferpa_officer_phone', { length: 50 }),
  ferpaAnnualNotification: boolean('ferpa_annual_notification').default(true),
  
  // Subscription and billing
  subscriptionTier: varchar('subscription_tier', { length: 50 }).default('basic'),
  maxStudents: integer('max_students').default(100),
  maxStorage: decimal('max_storage_gb', { precision: 10, scale: 2 }).default('10.00'),
  
  // Security settings
  requireTwoFactor: boolean('require_two_factor').default(false),
  passwordPolicy: text('password_policy'), // JSON configuration
  sessionTimeout: integer('session_timeout_minutes').default(60),
  
  // Status and configuration
  isActive: boolean('is_active').default(true),
  settings: text('settings'), // JSON configuration
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => users.id),
});

// Organization memberships - linking users to organizations
export const organizationMemberships = pgTable('organization_memberships', {
  id: uuid('id').primaryKey().defaultRandom(),
  organizationId: uuid('organization_id').references(() => organizations.id).notNull(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  
  // Membership details
  role: varchar('role', { length: 50 }).notNull(), // admin, teacher, student, parent
  title: varchar('title', { length: 100 }), // Job title or role description
  department: varchar('department', { length: 100 }),
  
  // Status and dates
  status: varchar('status', { length: 20 }).default('active'), // active, inactive, suspended
  joinedAt: timestamp('joined_at').defaultNow().notNull(),
  leftAt: timestamp('left_at'),
  
  // Permissions within the organization
  permissions: text('permissions'), // JSON array of specific permissions
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => users.id),
});

// Classrooms/Groups within organizations
export const classrooms = pgTable('classrooms', {
  id: uuid('id').primaryKey().defaultRandom(),
  organizationId: uuid('organization_id').references(() => organizations.id).notNull(),
  
  // Classroom information
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  subject: varchar('subject', { length: 100 }),
  gradeLevel: varchar('grade_level', { length: 50 }),
  
  // Academic year and terms
  academicYear: varchar('academic_year', { length: 20 }),
  term: varchar('term', { length: 50 }), // Fall, Spring, Summer, etc.
  
  // Classroom settings
  isActive: boolean('is_active').default(true),
  settings: text('settings'), // JSON configuration
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => users.id),
});

// Classroom enrollments - students and teachers in classrooms
export const classroomEnrollments = pgTable('classroom_enrollments', {
  id: uuid('id').primaryKey().defaultRandom(),
  classroomId: uuid('classroom_id').references(() => classrooms.id).notNull(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  
  // Enrollment details
  role: varchar('role', { length: 20 }).notNull(), // teacher, assistant, student
  status: varchar('status', { length: 20 }).default('active'), // active, inactive, completed
  
  // Enrollment period
  enrolledAt: timestamp('enrolled_at').defaultNow().notNull(),
  completedAt: timestamp('completed_at'),
  
  // Academic tracking
  finalGrade: varchar('final_grade', { length: 10 }),
  notes: text('notes'),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => users.id),
});

// Organization invitations for new users
export const organizationInvitations = pgTable('organization_invitations', {
  id: uuid('id').primaryKey().defaultRandom(),
  organizationId: uuid('organization_id').references(() => organizations.id).notNull(),
  
  // Invitation details
  email: varchar('email', { length: 255 }).notNull(),
  role: varchar('role', { length: 50 }).notNull(),
  token: varchar('token', { length: 255 }).unique().notNull(),
  
  // Status and expiration
  status: varchar('status', { length: 20 }).default('pending'), // pending, accepted, expired, revoked
  expiresAt: timestamp('expires_at').notNull(),
  acceptedAt: timestamp('accepted_at'),
  acceptedBy: uuid('accepted_by').references(() => users.id),
  
  // Invitation metadata
  message: text('message'),
  inviterName: varchar('inviter_name', { length: 255 }),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => users.id).notNull(),
});