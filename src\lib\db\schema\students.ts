import { pgTable, uuid, varchar, timestamp, boolean, text, integer, decimal, jsonb } from 'drizzle-orm/pg-core';
import { users, ageGroupEnum } from './auth';
import { organizations, classrooms } from './organizations';

// Student profiles with FERPA-compliant data handling
export const studentProfiles = pgTable('student_profiles', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull().unique(),
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Student identification (encrypted for FERPA compliance)
  studentId: varchar('student_id', { length: 100 }), // School-assigned student ID
  gradeLevel: varchar('grade_level', { length: 20 }),
  graduationYear: integer('graduation_year'),
  
  // Learning preferences and accommodations
  learningStyle: varchar('learning_style', { length: 50 }), // visual, auditory, kinesthetic, etc.
  accommodations: text('accommodations'), // IEP/504 accommodations
  specialNeeds: text('special_needs'), // Encrypted field for sensitive info
  
  // Academic preferences
  preferredSubjects: text('preferred_subjects'), // JSON array
  difficultSubjects: text('difficult_subjects'), // JSON array
  learningGoals: text('learning_goals'),
  
  // Parental controls and safety
  contentFilterLevel: varchar('content_filter_level', { length: 20 }).default('strict'),
  allowedTopics: text('allowed_topics'), // JSON array of approved topics
  restrictedTopics: text('restricted_topics'), // JSON array of restricted topics
  maxSessionTime: integer('max_session_time_minutes').default(60),
  
  // Progress tracking settings
  showProgressToParents: boolean('show_progress_to_parents').default(true),
  shareDataWithTeachers: boolean('share_data_with_teachers').default(true),
  
  // Emergency contact information (FERPA directory information)
  emergencyContactName: varchar('emergency_contact_name', { length: 255 }),
  emergencyContactPhone: varchar('emergency_contact_phone', { length: 50 }),
  emergencyContactRelation: varchar('emergency_contact_relation', { length: 50 }),
  
  // Status and settings
  isActive: boolean('is_active').default(true),
  settings: jsonb('settings'), // Additional user preferences
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  createdBy: uuid('created_by').references(() => users.id),
});

// Materials uploaded by students (with content safety checks)
export const materials = pgTable('materials', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id),
  classroomId: uuid('classroom_id').references(() => classrooms.id),
  
  // Material metadata
  name: varchar('name', { length: 255 }).notNull(),
  type: varchar('type', { length: 50 }).notNull(), // topic, text, file, pdf
  description: text('description'),
  
  // Content information
  content: text('content'), // Encrypted for sensitive content
  fileUrl: text('file_url'), // For uploaded files
  fileSize: integer('file_size'), // In bytes
  fileMimeType: varchar('file_mime_type', { length: 100 }),
  
  // Content safety and approval
  safetyStatus: varchar('safety_status', { length: 20 }).default('pending'), // pending, approved, rejected
  safetyScore: decimal('safety_score', { precision: 5, scale: 2 }), // 0.00 to 100.00
  safetyReview: text('safety_review'), // AI safety review results
  
  // Educational categorization
  subject: varchar('subject', { length: 100 }),
  topics: text('topics'), // JSON array of topics
  difficulty: varchar('difficulty', { length: 20 }), // beginner, intermediate, advanced
  ageGroup: ageGroupEnum('age_group'),
  
  // Usage and sharing
  isPublic: boolean('is_public').default(false),
  shareWithClassroom: boolean('share_with_classroom').default(false),
  shareWithOrganization: boolean('share_with_organization').default(false),
  
  // Status and metadata
  status: varchar('status', { length: 20 }).default('active'), // active, archived, deleted
  lastAccessedAt: timestamp('last_accessed_at'),
  accessCount: integer('access_count').default(0),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'), // Soft delete for FERPA compliance
});

// Quiz results and academic performance
export const quizResults = pgTable('quiz_results', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  materialId: uuid('material_id').references(() => materials.id).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id),
  classroomId: uuid('classroom_id').references(() => classrooms.id),
  
  // Quiz metadata
  quizTitle: varchar('quiz_title', { length: 255 }),
  materialName: varchar('material_name', { length: 255 }).notNull(),
  subject: varchar('subject', { length: 100 }),
  
  // Performance metrics
  score: integer('score').notNull(),
  totalQuestions: integer('total_questions').notNull(),
  timeSpent: integer('time_spent_seconds'),
  
  // Detailed results (encrypted for privacy)
  questionResults: jsonb('question_results'), // Array of question-level results
  
  // Learning analytics
  difficultyLevel: varchar('difficulty_level', { length: 20 }),
  topicsTestedJson: text('topics_tested'), // JSON array
  topicsStrong: text('topics_strong'), // JSON array
  topicsNeedWork: text('topics_need_work'), // JSON array
  
  // Performance categorization
  performanceLevel: varchar('performance_level', { length: 20 }), // excellent, good, fair, needs_improvement
  percentageScore: decimal('percentage_score', { precision: 5, scale: 2 }),
  
  // Status and sharing
  isSharedWithParents: boolean('is_shared_with_parents').default(true),
  isSharedWithTeachers: boolean('is_shared_with_teachers').default(true),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Student achievements and badges
export const achievements = pgTable('achievements', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Achievement details
  badgeId: varchar('badge_id', { length: 100 }).notNull(),
  badgeName: varchar('badge_name', { length: 255 }).notNull(),
  badgeDescription: text('badge_description'),
  badgeIcon: varchar('badge_icon', { length: 10 }), // Emoji or icon identifier
  
  // Achievement criteria
  criteria: text('criteria'), // How the badge was earned
  score: integer('score'), // Score when badge was earned
  totalQuestions: integer('total_questions'), // Total questions in qualifying quiz
  
  // Categorization
  category: varchar('category', { length: 50 }), // subject, streak, milestone, etc.
  difficulty: varchar('difficulty', { length: 20 }), // bronze, silver, gold
  
  // Status and visibility
  isVisible: boolean('is_visible').default(true),
  isSharedWithParents: boolean('is_shared_with_parents').default(true),
  isSharedWithTeachers: boolean('is_shared_with_teachers').default(true),
  
  // Audit fields
  unlockedAt: timestamp('unlocked_at').defaultNow().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Flip card sets generated by students
export const flipCards = pgTable('flip_cards', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  materialId: uuid('material_id').references(() => materials.id).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Card set metadata
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  subject: varchar('subject', { length: 100 }),
  
  // Card content (encrypted for privacy)
  cards: jsonb('cards').notNull(), // Array of {term, definition} objects
  cardCount: integer('card_count').notNull(),
  
  // Usage tracking
  studyCount: integer('study_count').default(0),
  lastStudiedAt: timestamp('last_studied_at'),
  
  // Sharing and visibility
  isSharedWithClassroom: boolean('is_shared_with_classroom').default(false),
  isSharedWithOrganization: boolean('is_shared_with_organization').default(false),
  
  // Status
  status: varchar('status', { length: 20 }).default('active'),
  
  // Audit fields
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Chat/AI interaction history (for safety monitoring)
export const chatSessions = pgTable('chat_sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id).notNull(),
  materialId: uuid('material_id').references(() => materials.id),
  organizationId: uuid('organization_id').references(() => organizations.id),
  
  // Session metadata
  sessionTitle: varchar('session_title', { length: 255 }),
  context: varchar('context', { length: 100 }), // quiz, material, general
  
  // Chat history (encrypted for privacy)
  messages: jsonb('messages').notNull(), // Array of {role, content, timestamp}
  messageCount: integer('message_count').default(0),
  
  // Safety monitoring
  safetyFlags: text('safety_flags'), // JSON array of safety concerns
  appropriatenessScore: decimal('appropriateness_score', { precision: 5, scale: 2 }),
  
  // Session metrics
  duration: integer('duration_seconds'),
  tokens: integer('tokens_used'),
  
  // Status
  status: varchar('status', { length: 20 }).default('active'),
  
  // Audit fields
  startedAt: timestamp('started_at').defaultNow().notNull(),
  endedAt: timestamp('ended_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});