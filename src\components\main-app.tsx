'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { Material, QuizResult, FlipCardSet, Achievement } from '@/lib/types';
import { generateFlipCards } from '@/ai/flows/generate-flip-cards';
import MaterialUploader from './material-uploader';
import ChatInterface from './chat-interface';
import QuizInterface from './quiz-interface';
import QuizHistoryTable from './quiz-history-table';
import FlipCardInterface from './flip-card-interface';
import ProgressDashboard from './progress-dashboard';
import { BrainCircuit, BookO<PERSON>, MessageSquare, GraduationCap, History, LogOut, Loader2, Trash2, Layers, TrendingUp } from 'lucide-react';

export default function MainApp() {
  const router = useRouter();
  const { toast } = useToast();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [selectedMaterialId, setSelectedMaterialId] = useState<string | null>(null);
  const [quizResults, setQuizResults] = useState<QuizResult[]>([]);
  const [flipCardSet, setFlipCardSet] = useState<FlipCardSet | null>(null);
  const [isGeneratingCards, setIsGeneratingCards] = useState(false);
  const [activeTab, setActiveTab] = useState('materials');
  const [achievements, setAchievements] = useState<Achievement[]>([]);


  useEffect(() => {
    const authStatus = localStorage.getItem('scholar-ai-auth') === 'true';
    if (!authStatus) {
      router.replace('/login');
    } else {
      setIsAuthenticated(true);
    }
  }, [router]);

  useEffect(() => {
    if (isAuthenticated) {
      try {
        const storedMaterials = localStorage.getItem('scholar-ai-materials');
        if (storedMaterials) {
          setMaterials(JSON.parse(storedMaterials));
        }
        const storedResults = localStorage.getItem('scholar-ai-results');
        if (storedResults) {
          setQuizResults(JSON.parse(storedResults));
        }
        const storedAchievements = localStorage.getItem('scholar-ai-achievements');
        if (storedAchievements) {
          setAchievements(JSON.parse(storedAchievements));
        }
      } catch (error) {
        console.error("Failed to parse data from localStorage", error);
        localStorage.removeItem('scholar-ai-materials');
        localStorage.removeItem('scholar-ai-results');
        localStorage.removeItem('scholar-ai-achievements');
      }
    }
  }, [isAuthenticated]);


  useEffect(() => {
    if (isAuthenticated) {
      localStorage.setItem('scholar-ai-materials', JSON.stringify(materials));
    }
  }, [materials, isAuthenticated]);

  useEffect(() => {
    if (isAuthenticated) {
      localStorage.setItem('scholar-ai-results', JSON.stringify(quizResults));
    }
  }, [quizResults, isAuthenticated]);

  useEffect(() => {
    if (isAuthenticated) {
      localStorage.setItem('scholar-ai-achievements', JSON.stringify(achievements));
    }
  }, [achievements, isAuthenticated]);

  const handleLogout = () => {
    localStorage.removeItem('scholar-ai-auth');
    router.push('/login');
    toast({ title: 'Logged Out', description: 'You have been successfully logged out.' });
  };

  const handleMaterialUpload = (name: string, content: string) => {
    const newMaterial: Material = { id: `mat_${Date.now()}`, name, content };
    setMaterials((prev) => [...prev, newMaterial]);
    setSelectedMaterialId(newMaterial.id);
  };
  
  const handleDeleteMaterial = (id: string) => {
    setMaterials(materials.filter(m => m.id !== id));
    if (selectedMaterialId === id) {
        setSelectedMaterialId(null);
    }
    if (flipCardSet?.materialId === id) {
      setFlipCardSet(null);
    }
    toast({ title: 'Material Deleted' });
  }

  const handleQuizComplete = (score: number, total: number) => {
    if (!selectedMaterialId) return;
    const material = materials.find((m) => m.id === selectedMaterialId);
    if (!material) return;

    const newResult: QuizResult = {
      materialId: material.id,
      materialName: material.name,
      score,
      total,
      date: new Date().toISOString(),
    };
    setQuizResults((prev) => [...prev, newResult]);
  };

  const handleNewAchievements = (newAchievements: Achievement[]) => {
    setAchievements((prev) => [...prev, ...newAchievements]);
  };

  const handleGenerateFlipCards = async (material: Material) => {
    setIsGeneratingCards(true);
    setSelectedMaterialId(material.id);
    try {
      const result = await generateFlipCards({ material: material.content, numberOfCards: 10 });
      const parsedCards = JSON.parse(result.cards);
      setFlipCardSet({
        materialId: material.id,
        materialName: material.name,
        cards: parsedCards.cards,
      });
      setActiveTab('flipcards');
      toast({
        title: 'Flip Cards Ready!',
        description: `Generated cards for ${material.name}.`,
      });
    } catch(error) {
      console.error("Error generating flip cards:", error);
      toast({
        title: 'Generation Failed',
        description: 'Could not generate flip cards from the material.',
        variant: 'destructive',
      });
    } finally {
      setIsGeneratingCards(false);
    }
  };

  const selectedMaterial = useMemo(
    () => materials.find((m) => m.id === selectedMaterialId) || null,
    [materials, selectedMaterialId]
  );
  
  if (isAuthenticated === null) {
      return (
        <div className="flex h-screen w-full flex-col items-center justify-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary"/>
            <p className="text-muted-foreground">Loading ScholarAI...</p>
        </div>
      )
  }

  return (
    <div className="min-h-screen flex flex-col">
        <header className="sticky top-0 z-10 flex items-center justify-between border-b bg-gradient-to-r from-blue-50 via-purple-50 to-cyan-50 border-purple-200 p-4 backdrop-blur-sm">
            <div className="flex items-center gap-2">
                <BrainCircuit className="h-7 w-7 text-purple-600 celebrate-shake" />
                <h1 className="font-headline text-2xl font-bold text-rainbow">ScholarAI</h1>
            </div>
            <Button variant="ghost" size="sm" onClick={handleLogout}>
                Logout
                <LogOut className="ml-2 h-4 w-4" />
            </Button>
        </header>

        <main className="flex-1 p-4 md:p-6 lg:p-8">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-3 md:grid-cols-6">
                    <TabsTrigger value="materials"><BookOpen className="mr-2 h-4 w-4"/>Materials</TabsTrigger>
                    <TabsTrigger value="chat"><MessageSquare className="mr-2 h-4 w-4"/>AI Chat</TabsTrigger>
                    <TabsTrigger value="quiz"><GraduationCap className="mr-2 h-4 w-4"/>Quiz</TabsTrigger>
                    <TabsTrigger value="flipcards"><Layers className="mr-2 h-4 w-4"/>Cards</TabsTrigger>
                    <TabsTrigger value="progress"><TrendingUp className="mr-2 h-4 w-4"/>Progress</TabsTrigger>
                    <TabsTrigger value="history"><History className="mr-2 h-4 w-4"/>History</TabsTrigger>
                </TabsList>
                <TabsContent value="materials" className="mt-6">
                    <div className="grid gap-6 md:grid-cols-2">
                        <div>
                            <Card>
                                <CardHeader>
                                    <CardTitle>Add Learning Material</CardTitle>
                                    <CardDescription>Enter a topic, paste text, or upload a file to start learning.</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <MaterialUploader onUpload={handleMaterialUpload} disabled={isGeneratingCards} />
                                </CardContent>
                            </Card>
                        </div>
                        <div>
                           <Card>
                                <CardHeader>
                                    <CardTitle>Uploaded Materials</CardTitle>
                                    <CardDescription>Select a material to interact with.</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    {materials.length > 0 ? (
                                        <ScrollArea className="h-[280px]">
                                            <div className="space-y-2">
                                            {materials.map(material => (
                                                <div key={material.id}
                                                    className={`rounded-md border p-3 transition-all hover-glow ${selectedMaterialId === material.id ? 'border-primary bg-gradient-to-r from-blue-50 to-purple-50 ring-2 ring-primary rainbow-border' : 'hover:bg-gradient-to-r hover:from-cyan-50 hover:to-blue-50'}`}>
                                                    <div className="flex items-center justify-between">
                                                      <p onClick={() => setSelectedMaterialId(material.id)} className="font-medium truncate pr-2 cursor-pointer flex-1">{material.name}</p>
                                                      <div className="flex items-center gap-1">
                                                        <Button 
                                                          variant="outline"
                                                          size="sm"
                                                          disabled={isGeneratingCards && selectedMaterialId === material.id}
                                                          onClick={() => handleGenerateFlipCards(material)}
                                                          style={{backgroundColor: '#00BCD4', color: 'white'}}
                                                          className="h-8"
                                                        >
                                                            {isGeneratingCards && selectedMaterialId === material.id ? (
                                                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                            ) : (
                                                              <Layers className="mr-2 h-4 w-4" />
                                                            )}
                                                            Cards
                                                        </Button>
                                                        <Button variant="ghost" size="icon" className="h-8 w-8 shrink-0" onClick={(e) => { e.stopPropagation(); handleDeleteMaterial(material.id); }}>
                                                            <Trash2 className="h-4 w-4 text-destructive/70 hover:text-destructive"/>
                                                        </Button>
                                                      </div>
                                                    </div>
                                                </div>
                                            ))}
                                            </div>
                                        </ScrollArea>
                                    ) : (
                                        <div className="flex h-64 items-center justify-center rounded-md border-2 border-dashed text-center">
                                            <p className="text-muted-foreground">Your uploaded materials will appear here.</p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </TabsContent>
                <TabsContent value="chat" className="mt-6">
                    <ChatInterface selectedMaterial={selectedMaterial} />
                </TabsContent>
                <TabsContent value="quiz" className="mt-6">
                    <QuizInterface 
                        selectedMaterial={selectedMaterial} 
                        onQuizComplete={handleQuizComplete}
                        quizResults={quizResults}
                        achievements={achievements}
                        onNewAchievements={handleNewAchievements}
                    />
                </TabsContent>
                <TabsContent value="flipcards" className="mt-6">
                    <FlipCardInterface flipCardSet={flipCardSet} />
                </TabsContent>
                <TabsContent value="progress" className="mt-6">
                    <ProgressDashboard quizResults={quizResults} achievements={achievements} />
                </TabsContent>
                <TabsContent value="history" className="mt-6">
                    <QuizHistoryTable results={quizResults} />
                </TabsContent>
            </Tabs>
        </main>
    </div>
  );
}
